<template>
  <div class="eop-containers">
    <navList :navList="navItems" :index="activeIndex" @setoriData="handleNavChange" class="nav-list" />
    <BDS v-if="activeIndex === 1" />
    <GLONASS v-if="activeIndex === 2" />
    <GPS v-if="activeIndex === 3" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import navList from "@/components/navList/navList.vue";
import { useRouter } from "vue-router";
import BDS from "./BDS.vue";
import GLONASS from "./GLONASS.vue";
import GPS from "./GPS.vue";
const router = useRouter();
const activeIndex = ref(1);

const navItems = [
  { id: 1, name: "BDS" },
  { id: 3, name: "GPS" },
  { id: 2, name: "GLONASS" },
];

// 处理导航切换
const handleNavChange = (id) => {
  activeIndex.value = id;
  // switch (id) {
  //   case 1:
  //     router.push("/dataManagement/rawTimeDiff/EOP/BDS");
  //     break;
  //   case 2:
  //     router.push("/dataManagement/rawTimeDiff/EOP/GLONASS");
  //     break;
  //   case 3:
  //     router.push("/dataManagement/rawTimeDiff/EOP/GPS");
  //     break;
  //   default:
  //     router.push("/dataManagement/rawTimeDiff/EOP/BDS");
  // }
};
</script>

<style scoped lang="scss">
.eop-containers {
  margin-top: 16px;
  padding: 16px;
  background: #fff;
  .nav-list {
    border-bottom: 1px solid #e5e5e5;
  }
}
</style>
