import { dayjs } from 'element-plus';
import { defineStore } from 'pinia';
import { computed, reactive, toRefs } from 'vue';
import utc from 'dayjs/plugin/utc';
import 'dayjs/locale/zh-cn';
import { SetupStoreId } from '@/enum';

let timer: number;
dayjs.locale('zh-cn');
dayjs.extend(utc)
   
export const useTimer = defineStore(
	SetupStoreId.Timer,
	() => {
		// states
		const data = reactive<any>({
			utcTime: 0,
		});

		// 初始化计时器
		if(!isNaN(timer)) {
			clearInterval(timer);
		}
		data.utcTime = new Date().getTime();
		/* 每秒都必须重新获取最小时间，因为在浏览器非激活状态，setInterval调用间隔会自动变长，
		导致时间不准确 */
		timer = setInterval(() => {
			data.utcTime = new Date().getTime();
		}, 1000);

		// getters

		const utcTimeValue = computed(() => {
			return dayjs.utc(data.utcTime).format('YYYY年MM月DD日 HH:mm:ss');
		});

		const beijingTimeValue = computed(() => {
			return dayjs(data.utcTime).format('YYYY年MM月DD日 HH:mm:ss dddd');
		});

		const beijingTime1Value = computed(() => {
			return dayjs(data.utcTime).format('HH:mm:ss');
		});

		// actions
		return {
			...toRefs(data),
			utcTimeValue,
			beijingTimeValue,
			beijingTime1Value,
		};
	}
);
