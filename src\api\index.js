// 引用axios
import axios from "axios";
import { dispose } from "@/api/ws";
import { localStg } from '@/utils/storage';
import { useRouterPush } from '@/hooks/common/router';
import { ElNotification, ElMessage } from 'element-plus'
// const pageConfig = require("../config/config");
// import common from "../utils/uCommon"; //公共方法


import {
  apiEncryptHandle,
  apiDecryptHandle,
} from "./utils/uCrypto"; //aes加密

// 自定义判断元素类型JS
function toType(obj) {
  return {}.toString
    .call(obj)
    .match(/\s([a-zA-Z]+)/)[1]
    .toLowerCase();
}

// 参数过滤函数
function filterNull(o) {
  for (var key in o) {
    if (o[key] === null) {
      delete o[key];
    }
    if (toType(o[key]) === "string") {
      o[key] = o[key].trim();
    } else if (toType(o[key]) === "object") {
      o[key] = filterNull(o[key]);
    } else if (toType(o[key]) === "array") {
      o[key] = filterNull(o[key]);
    }
  }
  return o;
}
/*
  接口处理函数
  这个函数每个项目都是不一样的，我现在调整的是适用于
  https://cnodejs.org/api/v1 的接口，如果是其他接口
  需要根据接口的参数进行调整。参考说明文档地址：
  https://cnodejs.org/topic/5378720ed6e2d16149fa16bd
  主要是，不同的接口的成功标识和失败提示是不一致的。
  另外，不同的项目的处理方法也是不一致的，这里出错就是简单的alert
*/
function apiAxios(method, url, params, prompt = { info: false }, channelType, header) {
  ElMessage.closeAll(); // 修改为closeAll
  ElNotification.closeAll(); // 修改为closeAll
  if (params) {
    params = filterNull(params);
  }
  let apiData = apiEncryptHandle(url, params, header, channelType);
  params = apiData.params;
  return axios({
    method: method,
    url: apiData.url,
    data: method === "POST" || method === "PUT" ? params : null,
    params:
      method === "GET" || method === "DELETE" ? JSON.stringify(params) : null,
    headers: apiData.headers,
    baseURL: apiData.baseURL,
  })
    .then(function (res) {


      //先这样处理
      // const response = apiDecryptHandle(res.data, channelType);
      const response = res.data

      if (response.code == 502 || response.status == 502) {
        gologin()
        return
      }
      if (response.status == 200 || response.code == 200) {
        if (prompt.info) {
          ElMessage.success(prompt.success || response.msg)
        }
        return response
      } else {
        if (prompt.info) {
          ElMessage.error(prompt.error || response.msg)
        }
        return new Promise(resolve => setTimeout(resolve, 1500)).then(() => response)
      }
    })
    .catch(function (err) {
      if (err) {
        if (err.response.data.status == 502) {
          gologin()
        }
        if (prompt.info) {
          if (err.response.status == 500) {
            ElMessage.error('服务器错误')
          } else {
            ElMessage.error(err.response.data.msg)
          }
        }
      }
    });
}
// get post 请求怎么搞
async function getPost(url, params, prompt) {
  let newUlr = 'http://192.168.1.236:20004' + url;
  const headers = {
    "accept": "*/*",
    "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8"
  };
  const token = localStg.get('token') || false;
  if (token) {
    headers.Authorization = token;
  }
  const data = new URLSearchParams({ ...params });
  return axios.post(newUlr, data, { headers: headers })
}

// 处理账户过期逻辑的函数
const gologin = () => {
  // 退出登录令牌过期
  ElNotification({
    title: '',
    message: '账户已过期，请重新登录',
    type: 'warning',
    duration: 0,
  })
  setTimeout(() => {
    const { toLogin } = useRouterPush(false);
    console.log("我要退出登录了")
    localStg.remove('token')
    localStg.remove('refreshToken')
    dispose()
    toLogin()
  }, 600)
}

// 封装axios请求方法的对象
var apiAjax = {
  get: function (url, params, prompt, header, channelType) {
    return apiAxios("GET", url, params, prompt, header, channelType);
  },
  post: function (url, params, prompt, channelType, header, channel) {
    return apiAxios("POST", url, params, prompt, channelType, header, channel);
  },
  put: function (url, params, prompt, channelType) {
    return apiAxios("PUT", url, params, prompt, channelType);
  },
  delete: function (url, params, prompt, channelType) {
    return apiAxios("DELETE", url, params, prompt, channelType);
  },
  getPsot: function (url, params, prompt, channelType) {
    return getPost(url, params);
  },
};
export default apiAjax;
export const $http = apiAjax;
