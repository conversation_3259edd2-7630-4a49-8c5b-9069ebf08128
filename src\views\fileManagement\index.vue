<template>
  <div class="fileManagement">
    <div class="oriData_nav">
      <div
        @click="setoriData('1')"
        :class="{
          oriData_nav_item: true,
          oriData_nav_info: businessApp == '1',
        }"
      >
        <el-icon><Tickets /></el-icon><span>外部输入文件</span>
      </div>
      <div
        @click="setoriData('2')"
        :class="{
          oriData_nav_item: true,
          oriData_nav_info: businessApp == '2',
        }"
      >
        <el-icon><Tickets /></el-icon><span>内部产生文件</span>
      </div>
    </div>
    <div class="oriData_list">
      <el-form
        :inline="true"
        class="filter-form"
        @submit.prevent="handleSearch"
      >
        <el-form-item label="文件类型">
          <el-select
            style="width: 200px"
            v-model="fileType"
            placeholder="请选择"
            clearable
          >
            <el-option
              v-for="item in fileTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="时间范围">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item label="文件名称">
          <el-input v-model="searchInput" placeholder="请输入搜索内容" />
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            native-type="submit"
            icon="Search"
            class="search-btn"
          >
            搜索
          </el-button>
        </el-form-item>
      </el-form>

      <el-table
        :data="fileList"
        style="width: 100%"
        v-loading="loading"
        border
        class="custom-table"
      >
        <el-table-column prop="fileName" label="文件名称" />
        <!-- <el-table-column prop="createTime" label="文件生成时间" /> -->
        <el-table-column prop="downloadTime" label="文件时间" />
        <el-table-column prop="dataType" label="文件类型" />
        <el-table-column prop="fileSourcePath" label="文件来源" />
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button
              text
              type="primary"
              @click="handleDownload(scope.row)"
              class="action-btn"
            >
              <el-icon><Download /></el-icon>下载
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import { Search, Download, View } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import apiAjax from "@/api/index";
import configure from "@/utils/configure.js";

const businessApp = ref("1");
const setoriData = (name) => {
  businessApp.value = name;
  fetchFileList();
};

// 筛选相关数据
const fileType = ref("all");
const dateRange = ref([]);
const searchInput = ref("");
const fileTypeOptions = [
  { label: "全部", value: "all" },
  { label: "ERP_IERS", value: "ERP_IERS" },
  { label: "ERP_NTSC", value: "ERP_NTSC" },
  { label: "UTCr", value: "UTCr" },
  { label: "GNSST_GPS", value: "GNSST_GPS" },
  { label: "GNSST_GAL", value: "GNSST_GAL" },
  { label: "GNSST_GLO", value: "GNSST_GLO" },
  { label: "GNSST_BDS", value: "GNSST_BDS" },
];

// 表格数据
const loading = ref(false);
const fileList = ref([]);
const currentPage = ref(1);
const pageSize = ref(10);
const total = ref(0);

const fetchFileList = async () => {
  loading.value = true;
  const getUrls = getUrl();
  let { data } = await apiAjax.get(getUrls);
  fileList.value = data.content;
  total.value = data.totalElements;
  loading.value = false;
};

const getUrl = () => {
  let rul = "/api/jnx/fusion/apps/satellite/fileInfo/findByPage";
  const params = {
    page: currentPage.value,
    size: pageSize.value,
    dataType: fileType.value == "all" ? "" : fileType.value,
    beginTime:
      dateRange.value?.length > 0
        ? dateRange.value[0].toISOString().split("T")[0] + " 00:00:00"
        : "",
    endTime:
      dateRange.value?.length > 0
        ? dateRange.value[1].toISOString().split("T")[0] + " 23:59:59"
        : "",
    fileName: searchInput.value,
  };
  let url = "";
  Object.entries(params).forEach(([key, value]) => {
    if (value && value != "") {
      url += `${key}=${value}&`;
    }
  });
  return rul + "?" + url.slice(0, -1);
};

// 搜索处理
const handleSearch = () => {
  currentPage.value = 1;
  fetchFileList();
};

// 下载处理
const handleDownload = async (row) => {
  configure.download(row.id);
};

// 分页处理
const handleSizeChange = (val) => {
  pageSize.value = val;
  fetchFileList();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  fetchFileList();
};

// 页面加载时获取数据
onMounted(() => {
  fetchFileList();
});
</script>

<style scoped lang="scss">
.fileManagement {
  width: 100%;
  // min-height: 93vh;
  background-color: #f5f7fa;
  margin-top: 16px;

  .oriData_nav {
    background: #ffffff;
    display: flex;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 16px;

    .oriData_nav_item {
      margin-right: 20px;
      display: flex;
      align-items: center;
      padding: 5px 12px;
      cursor: pointer;
      transition: all 0.3s;

      .el-icon {
        margin-right: 8px;
        font-size: 16px;
      }

      &:hover {
        color: #0052d9;
        background: #ecf2fe;
        border-radius: 3px;
      }
    }

    .oriData_nav_info {
      background: #ecf2fe;
      border-radius: 3px;
      color: #0052d9;
    }
  }

  .oriData_list {
    background: #ffffff;
    padding: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

    .filter-form {
      background: #f8f9fc;
      padding: 24px;
      border-radius: 8px;
      margin-bottom: 24px;

      .el-form-item {
        margin-bottom: 0;
        margin-right: 20px;

        &:last-child {
          margin-right: 0;
          margin-left: auto;
        }
      }

      .search-btn,
      .export-btn {
        min-width: 100px;
        height: 36px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
    }

    .custom-table {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);

      :deep(.el-table__header) {
        background-color: #f8f9fc;
        font-weight: 600;
      }

      :deep(.el-table__row) {
        transition: all 0.3s ease;

        &:hover {
          background-color: #f8f9fc;
        }
      }

      .action-btn {
        padding: 4px 12px;
        margin: 0 4px;

        .el-icon {
          margin-right: 4px;
        }

        &:hover {
          background-color: #f0f6ff;
          border-radius: 4px;
        }
      }
    }

    .pagination-container {
      margin-top: 24px;
      display: flex;
      justify-content: flex-end;
      padding: 16px 0;
    }
  }
}

@media only screen and (max-height: 1080px) {
  .custom-table {
    height: 650px;
  }
}
@media only screen and (max-height: 919px) {
  .custom-table {
    height: 520px;
  }
}
</style>
