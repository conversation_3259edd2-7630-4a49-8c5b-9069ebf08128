<template>
  <div class="interface-status">
    <div class="status-header">
      <div class="status-title">接口状态监控</div>
      <div class="time-display text-4xl font-bold text-blue-600">
        {{ currentTime }}
      </div>
    </div>
    <div class="flow-chart">
      <!-- 顶部设备 -->
      <div class="top-row">
        <div class="device-line">
          <div v-for="(item, index) in topDevices" :key="index" class="device-wrapper">
            <div class="device-item">
              <div class="device-img"></div>
              <div class="device-info">
                <div class="status-dot" :class="{ 'status-error': !item.info }"></div>
                <div class="device-name">{{ item.name }}</div>
              </div>
              <div class="device-id">{{ item.id }}</div>
            </div>
            <div class="vertical-line">
              <div :class="item.direction === 'down' ? 'arrow-down' : 'arrow-up'"></div>
            </div>
          </div>
        </div>
        <!-- 顶部横向连接线 -->
        <div class="horizontal-line"></div>
      </div>

      <!-- 中间交换机 -->
      <div class="middle-row">
        <div class="switch-box">
          <div class="switch-img"></div>
          <div class="device-name">交换机</div>
        </div>
      </div>

      <!-- 底部设备 -->
      <div class="bottom-row">
        <div class="device-line">
          <div v-for="(device, index) in bottomDevices" :key="index" class="device-wrapper">
            <div class="vertical-line">
              <div :class="device.direction === 'up' ? 'arrow-up' : 'arrow-down'"></div>
            </div>
            <div class="device-item">
              <div class="device-img"></div>
              <div class="device-info">
                <div class="status-dot" :class="{ 'status-error': !device.info }"></div>
                <div class="device-name">{{ device.name }}</div>
              </div>
              <div class="device-id">{{ device.id }}</div>
            </div>
          </div>
        </div>
        <!-- 底部横向连接线 -->
        <div class="horizontal-line-bottom"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const topDevices = ref([
  { name: '工控机', id: 'IPC-001', direction: 'down', info: true },
  { name: '接收机', id: 'RCV-001', direction: 'down', info: true },
  { name: '时间间隔计数器', id: 'TIC-001', direction: 'down', info: false },
  { name: '工控机', id: 'IPC-002', direction: 'down', info: true },
  { name: '接收机', id: 'RCV-002', direction: 'down', info: true },
  { name: '时间间隔计数器', id: 'TIC-002', direction: 'down', info: true },
  { name: '工控机', id: 'IPC-003', direction: 'down', info: true },
  { name: '接收机', id: 'RCV-003', direction: 'down', info: false },
  { name: '时间间隔计数器', id: 'TIC-003', direction: 'down', info: true }
]);

const bottomDevices = ref([
  { name: 'FTP服务器', id: 'FTP-001', direction: 'down', info: true },
  { name: 'GNSS监测平台', id: 'GNSS-001', direction: 'up', info: true },
  { name: '1603平台', id: '1603-001', direction: 'up', info: false },
  { name: '卫星授时性能监测软件服务器', id: 'SAT-001', direction: 'up', info: true },
  { name: 'IGMAS', id: 'IGMAS-001', direction: 'up', info: true },
  { name: '频率分配放大器', id: 'FDA-001', direction: 'up', info: true },
  { name: '脉冲分配放大器', id: 'PDA-001', direction: 'up', info: false },
  { name: '自动气象站', id: 'AWS-001', direction: 'up', info: true }
]);
</script>

<style lang="scss" scoped>
.interface-status {
  margin-top: 16px;
  background: #fff;
  padding: 16px 24px;
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);

  .status-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 16px;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 24px;
  }

  .status-title {
    font-size: 18px;
    font-weight: 600;
    color: #1a1a1a;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 16px;
      background: #409EFF;
      border-radius: 2px;
    }
  }

  .time-display {
    color: #409EFF;
  }

  .flow-chart {
    position: relative;
    .top-row, .bottom-row {
      position: relative;
      margin-bottom: 40px;

      .device-line {
        display: flex;
        justify-content: space-between;
        padding: 0 20px;
        position: relative;

        .device-wrapper {
          display: flex;
          flex-direction: column;
          align-items: center;
          width: 85px;

          .device-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-bottom: 8px;

            .device-img {
              width: 80px;
              height: 25px;
              background: linear-gradient(to bottom, #f5f7fa, #ecf5ff);
              border: 1px solid #dcdfe6;
              border-radius: 4px;
              margin-bottom: 8px;
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
              }
            }

            .device-info {
              display: flex;
              align-items: center;
              gap: 4px;

              .status-dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: #67C23A;

                &.status-error {
                  background-color: #F56C6C;
                }
              }
            }

            .device-name {
              font-size: 12px;
              color: #409EFF;
              white-space: nowrap;
            }

            .device-id {
              font-size: 11px;
              color: #909399;
              margin-top: 4px;
            }
          }

          .vertical-line {
            position: relative;
            width: 2px;
            height: 35px;
            background: #67C23A;

            .arrow-down {
              position: absolute;
              bottom: -4px;
              left: -4px;
              width: 0;
              height: 0;
              border-left: 5px solid transparent;
              border-right: 5px solid transparent;
              border-top: 8px solid #67C23A;
            }

            .arrow-up {
              position: absolute;
              top: -4px;
              left: -4px;
              width: 0;
              height: 0;
              border-left: 5px solid transparent;
              border-right: 5px solid transparent;
              border-bottom: 8px solid #67C23A;
            }
          }
        }
      }

      .horizontal-line {
        position: absolute;
        bottom: 0;
        left: calc(20px + 41.5px);
        right: calc(20px + 41.5px);
        height: 2px;
        background: #67C23A;
      }

      .horizontal-line-bottom {
        position: absolute;
        top: 0;
        left: calc(20px + 41.5px);
        right: calc(20px + 41.5px);
        height: 2px;
        background: #67C23A;
      }
    }

    .bottom-row {
      .device-wrapper {
        .device-item {
          .device-img {
            background: #f0f9eb;
            border-color: #e1f3d8;
          }

          .device-info {
            .status-dot {
              background-color: #67C23A;

              &.status-error {
                background-color: #F56C6C;
              }
            }
          }

          .device-name {
            color: #67C23A;
            text-align: center;
            word-break: break-all;
          }

          .device-id {
            color: #909399;
            margin-top: 4px;
          }
        }
      }
    }

    .middle-row {
      display: flex;
      justify-content: center;
      margin: 10px 0;

      .switch-box {
        display: flex;
        flex-direction: column;
        align-items: center;

        .switch-img {
          width: 120px;
          height: 30px;
          background: linear-gradient(to right, #409EFF, #3a8ee6);
          border-radius: 4px;
          margin-bottom: 8px;
          box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-2px);
          }
        }

        .device-name {
          font-size: 12px;
          color: #409EFF;
        }
      }
    }
  }
}
</style>
