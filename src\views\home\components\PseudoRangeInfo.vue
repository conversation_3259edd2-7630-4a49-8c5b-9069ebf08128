<template>
  <div class="pseudo-range-info">
    <div>按钮</div>
    <div class="chart-content">
      <line-chart :data="lineChartOption" ref="pseudoRangeChartRef" />
    </div>
    <div class="tables-section">
      <div class="wrapper">
        <div class="table-title">卫星伪码授时偏差</div>
        <el-table :data="timeDiffList" border style="width: 100%" height="250">
          <el-table-column prop="time" label="UTC-时间" width="180" />
          <el-table-column prop="satelliteId" label="卫星号" width="100" />
          <el-table-column prop="timeDiff" label="时差(ns)" />
        </el-table>
      </div>
      <div class="table-wrapper">
        <div class="table-title">卫星伪码授时偏差模型参数</div>
        <el-table
          :data="modelParamsList"
          border
          style="width: 100%"
          height="250"
        >
          <el-table-column
            prop="generateTime"
            label="参数生成时间(UTC)"
            width="180"
          />
          <el-table-column prop="satelliteId" label="卫星号" width="100" />
          <el-table-column prop="frequencyId" label="频点" />
          <el-table-column prop="TOC" label="TOC" width="180" />
          <el-table-column prop="A0" label="A0(1E-10s)" />
          <el-table-column prop="A1" label="A1(1E-16s/s)" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import LineChart from "@/components/lineChart/lineChart.vue";
import { useHome } from "@/store/modules/home";
import apiAjax from "@/api/index";
const HomeData = useHome();
import { useAllSatellite } from "@/store/modules/allSatellite";
const allSatelliteStore = useAllSatellite();
// 修改图表配置
const lineChartOption = ref({
  xAxis: {
    type: "category",
    data: [],
  },
  yAxis: {
    type: "value",
    name: "时差(ns)",
    scale: true, // 自动缩放
    min: function (value) {
      return Math.floor(value.min - 5); // 最小值减5
    },
    max: function (value) {
      return Math.ceil(value.max + 5); // 最大值加5
    },
  },
  legend: {
    type: "scroll",
    bottom: 10,
    data: [],
  },
  grid: {
    top: "30px",
    left: "50px",
    right: "30px",
    bottom: "60px",
  },
  tooltip: {
    trigger: "axis",
    formatter: function (params) {
      const timeLabel = params[0].name;
      let result = `${timeLabel}<br/>`;
      params.forEach((param) => {
        if (param.value !== null && param.value !== undefined) {
          result += `${param.seriesName}: ${param.value} ns<br/>`;
        }
      });
      return result;
    },
  },
  series: [],
});

const modelParamsList = ref([]);
const timeDiffList = ref([]);
const pseudoRangeChartRef = ref();

// 生成时间轴数据（按分钟）
const generateTimeAxis = (startTime, endTime) => {
  const start = new Date(startTime);
  const end = new Date(endTime);
  const timeAxis = [];

  const current = new Date(start);
  while (current <= end) {
    timeAxis.push(
      current
        .toLocaleString("zh-CN", {
          // month: "2-digit",
          // day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        })
        .replace(/\//g, "-"),
    );
    current.setMinutes(current.getMinutes() + 1);
  }

  return timeAxis;
};

// 处理图表数据
const processChartData = (chartValues) => {
  const series = [];
  const legendData = [];

  Object.keys(chartValues).forEach((satellitePrn) => {
    const frequencies = chartValues[satellitePrn];

    Object.keys(frequencies).forEach((frequencyId) => {
      const seriesName = `${satellitePrn}(${frequencyId})`;
      legendData.push(seriesName);

      series.push({
        name: seriesName,
        type: "line",
        data: frequencies[frequencyId],
        connectNulls: false, // 不连接空值
        symbol: "circle",
        symbolSize: 4,
        lineStyle: {
          width: 2,
        },
      });
    });
  });

  return { series, legendData };
};

// 处理卫星伪码授时偏差表格数据
const processTimeDiffData = (predictValues) => {
  return predictValues.map((item) => ({
    time: item.timestamp,
    satelliteId: item.satellitePrn,
    timeDiff: item.frequencyBias,
  }));
};

// 处理模型参数表格数据
const processModelParamsData = (modelParams) => {
  return modelParams.map((item) => ({
    generateTime: item.generateTime,
    satelliteId: item.satellitePrn,
    TOC: item.timestamp,
    A0: item.a0,
    A1: item.a1,
    frequencyId: item.frequencyId,
  }));
};

const getPseudoRangeInfo = async () => {
  try {
    const { data } = await apiAjax.get(
      "/api/jnx/fusion/apps/satellite/home/<USER>/timeDiff/wxwmsspc/getLatest",
    );
    console.log("data", data);

    if (data) {
      HomeData.setNavListTimingData(data.systemFreqIdToPredictValue);
      // 生成时间轴
      const timeAxis = generateTimeAxis(data.startTime, data.endTime);

      // 处理图表数据
      const { series, legendData } = processChartData(data.chartValues);

      // 更新图表配置
      lineChartOption.value = {
        ...lineChartOption.value,
        xAxis: {
          ...lineChartOption.value.xAxis,
          data: timeAxis,
        },
        legend: {
          ...lineChartOption.value.legend,
          data: legendData,
        },
        series: series,
      };

      // 处理表格数据
      timeDiffList.value = processTimeDiffData(data.predictValues || []);
      modelParamsList.value = processModelParamsData(data.modelParams || []);

      // 更新图表
      if (pseudoRangeChartRef.value) {
        pseudoRangeChartRef.value.setInfos();
        pseudoRangeChartRef.value.getIns().setOption(lineChartOption.value);
      }
    }
  } catch (error) {
    console.error("获取伪码数据失败:", error);
  }
};
const setAllsatellite = (data) => {
  let newData = data.map((item) => {
    return {
      name: item.systemName,
      value: item.satelliteInfos.map((i) => i.prn),
    };
  });
  console.log("newData", newData);
};

onMounted(async () => {
  await allSatelliteStore.initAllData();
  setAllsatellite(allSatelliteStore.data);
  HomeData.setChartfn("pseudoRange", getPseudoRangeInfo);
});
</script>

<style lang="scss" scoped>
.pseudo-range-info {
  .chart-content {
    height: 420px;
    margin-bottom: 20px;
  }

  .tables-section {
    display: flex;
    gap: 20px;
    margin-top: 20px;

    .table-wrapper,
    .wrapper {
      background-color: #fff;
      border-radius: 4px;
      padding: 15px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .table-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #303133;
        padding-left: 5px;
        border-left: 4px solid #409eff;
      }

      .pagination-container {
        display: none;
      }
    }
    .table-wrapper {
      flex: 1;
    }
    .wrapper {
      width: 600px;
    }
  }
}
</style>
