<template>
  <div class="pseudo-range-info">
    <div class="header-section">
      <el-button
        type="primary"

        @click="showSatelliteDialog = true"
        class="satellite-select-btn"
      >
        <span>选择卫星</span>
        <span class="selected-count" v-if="selectedSatellites.length > 0">
          ({{ selectedSatellites.length }})
        </span>
      </el-button>
    </div>

    <!-- 卫星选择弹窗 -->
    <el-dialog
      v-model="showSatelliteDialog"
      title="选择卫星"
      width="800px"
      :before-close="handleDialogClose"
      class="satellite-dialog"
    >
      <div class="dialog-content">
        <div class="dialog-header">
          <div class="selection-summary">
            <span class="total-count">总计: {{ getTotalSatelliteCount() }} 颗卫星</span>
            <span class="selected-summary">已选择: {{ selectedSatellites.length }} 颗</span>
          </div>
          <div class="action-buttons">
            <el-button size="small" @click="selectAll">全选</el-button>
            <el-button size="small" @click="clearAll">清空</el-button>
          </div>
        </div>

        <div class="satellite-systems">
          <el-collapse v-model="activeCollapse" accordion>
            <el-collapse-item
              v-for="system in satelliteData"
              :key="system.name"
              :name="system.name"
              class="system-collapse"
            >
              <template #title>
                <div class="collapse-title" @click.stop>
                  <div class="system-info">
                    <el-checkbox
                      :model-value="isSystemFullySelected(system)"
                      :indeterminate="isSystemPartiallySelected(system)"
                      @change="toggleSystemSelection(system)"
                      class="system-checkbox"
                      @click.stop
                    />
                    <span class="system-name">{{ system.name }}</span>
                    <span class="system-count">({{ system.value.length }}颗)</span>
                  </div>
                  <span class="selected-in-system">
                    已选: {{ getSelectedInSystem(system).length }}/{{ system.value.length }}
                  </span>
                </div>
              </template>

              <div class="satellites-container">
                <div class="satellites-grid">
                  <div
                    v-for="satellite in system.value"
                    :key="satellite"
                    class="satellite-item"
                    :class="{ 'selected': selectedSatellites.includes(satellite) }"
                    @click="toggleSatellite(satellite)"
                  >
                    <el-checkbox
                      :model-value="selectedSatellites.includes(satellite)"
                      @change="toggleSatellite(satellite)"
                      class="satellite-checkbox"
                    />
                    <span class="satellite-name">{{ satellite }}</span>
                  </div>
                </div>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="showSatelliteDialog = false">取消</el-button>
          <el-button type="primary" @click="confirmSelection">
            确定 ({{ selectedSatellites.length }})
          </el-button>
        </div>
      </template>
    </el-dialog>

    <div class="chart-content">
      <line-chart :data="lineChartOption" ref="pseudoRangeChartRef" />
    </div>
    <div class="tables-section">
      <div class="wrapper">
        <div class="table-title">卫星伪码授时偏差</div>
        <el-table :data="timeDiffList" border style="width: 100%" height="250">
          <el-table-column prop="time" label="UTC-时间" width="180" />
          <el-table-column prop="satelliteId" label="卫星号" width="100" />
          <el-table-column prop="timeDiff" label="时差(ns)" />
        </el-table>
      </div>
      <div class="table-wrapper">
        <div class="table-title">卫星伪码授时偏差模型参数</div>
        <el-table
          :data="modelParamsList"
          border
          style="width: 100%"
          height="250"
        >
          <el-table-column
            prop="generateTime"
            label="参数生成时间(UTC)"
            width="180"
          />
          <el-table-column prop="satelliteId" label="卫星号" width="100" />
          <el-table-column prop="frequencyId" label="频点" />
          <el-table-column prop="TOC" label="TOC" width="180" />
          <el-table-column prop="A0" label="A0(1E-10s)" />
          <el-table-column prop="A1" label="A1(1E-16s/s)" />
        </el-table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import LineChart from "@/components/lineChart/lineChart.vue";
import { useHome } from "@/store/modules/home";
import apiAjax from "@/api/index";
// import { Satellite } from '@element-plus/icons-vue';
const HomeData = useHome();
import { useAllSatellite } from "@/store/modules/allSatellite";
const allSatelliteStore = useAllSatellite();

// 弹窗相关数据
const showSatelliteDialog = ref(false);
const selectedSatellites = ref([]);
const satelliteData = ref([]);
const activeCollapse = ref('');
// 修改图表配置
const lineChartOption = ref({
  xAxis: {
    type: "category",
    data: [],
  },
  yAxis: {
    type: "value",
    name: "时差(ns)",
    scale: true, // 自动缩放
    min: function (value) {
      return Math.floor(value.min - 5); // 最小值减5
    },
    max: function (value) {
      return Math.ceil(value.max + 5); // 最大值加5
    },
  },
  legend: {
    type: "scroll",
    bottom: 10,
    data: [],
  },
  grid: {
    top: "30px",
    left: "50px",
    right: "30px",
    bottom: "60px",
  },
  tooltip: {
    trigger: "axis",
    formatter: function (params) {
      const timeLabel = params[0].name;
      let result = `${timeLabel}<br/>`;
      params.forEach((param) => {
        if (param.value !== null && param.value !== undefined) {
          result += `${param.seriesName}: ${param.value} ns<br/>`;
        }
      });
      return result;
    },
  },
  series: [],
});

const modelParamsList = ref([]);
const timeDiffList = ref([]);
const pseudoRangeChartRef = ref();

// 生成时间轴数据（按分钟）
const generateTimeAxis = (startTime, endTime) => {
  const start = new Date(startTime);
  const end = new Date(endTime);
  const timeAxis = [];

  const current = new Date(start);
  while (current <= end) {
    timeAxis.push(
      current
        .toLocaleString("zh-CN", {
          // month: "2-digit",
          // day: "2-digit",
          hour: "2-digit",
          minute: "2-digit",
        })
        .replace(/\//g, "-"),
    );
    current.setMinutes(current.getMinutes() + 1);
  }

  return timeAxis;
};

// 处理图表数据
const processChartData = (chartValues) => {
  const series = [];
  const legendData = [];

  Object.keys(chartValues).forEach((satellitePrn) => {
    const frequencies = chartValues[satellitePrn];

    Object.keys(frequencies).forEach((frequencyId) => {
      const seriesName = `${satellitePrn}(${frequencyId})`;
      legendData.push(seriesName);

      series.push({
        name: seriesName,
        type: "line",
        data: frequencies[frequencyId],
        connectNulls: false, // 不连接空值
        symbol: "circle",
        symbolSize: 4,
        lineStyle: {
          width: 2,
        },
      });
    });
  });

  return { series, legendData };
};

// 处理卫星伪码授时偏差表格数据
const processTimeDiffData = (predictValues) => {
  return predictValues.map((item) => ({
    time: item.timestamp,
    satelliteId: item.satellitePrn,
    timeDiff: item.frequencyBias,
  }));
};

// 处理模型参数表格数据
const processModelParamsData = (modelParams) => {
  return modelParams.map((item) => ({
    generateTime: item.generateTime,
    satelliteId: item.satellitePrn,
    TOC: item.timestamp,
    A0: item.a0,
    A1: item.a1,
    frequencyId: item.frequencyId,
  }));
};

const getPseudoRangeInfo = async () => {
  try {
    const { data } = await apiAjax.get(
      "/api/jnx/fusion/apps/satellite/home/<USER>/timeDiff/wxwmsspc/getLatest",
    );
    console.log("data", data);

    if (data) {
      HomeData.setNavListTimingData(data.systemFreqIdToPredictValue);
      // 生成时间轴
      const timeAxis = generateTimeAxis(data.startTime, data.endTime);

      // 处理图表数据
      const { series, legendData } = processChartData(data.chartValues);

      // 更新图表配置
      lineChartOption.value = {
        ...lineChartOption.value,
        xAxis: {
          ...lineChartOption.value.xAxis,
          data: timeAxis,
        },
        legend: {
          ...lineChartOption.value.legend,
          data: legendData,
        },
        series: series,
      };

      // 处理表格数据
      timeDiffList.value = processTimeDiffData(data.predictValues || []);
      modelParamsList.value = processModelParamsData(data.modelParams || []);

      // 更新图表
      if (pseudoRangeChartRef.value) {
        pseudoRangeChartRef.value.setInfos();
        pseudoRangeChartRef.value.getIns().setOption(lineChartOption.value);
      }
    }
  } catch (error) {
    console.error("获取伪码数据失败:", error);
  }
};
const setAllsatellite = (data) => {
  let newData = data.map((item) => {
    return {
      name: item.systemName,
      value: item.satelliteInfos.map((i) => i.prn),
    };
  });
  console.log("newData", newData);
  satelliteData.value = newData;
};

// 弹窗相关方法
const getTotalSatelliteCount = () => {
  return satelliteData.value.reduce((total, system) => total + system.value.length, 0);
};

const isSystemFullySelected = (system) => {
  return system.value.every(satellite => selectedSatellites.value.includes(satellite));
};

const isSystemPartiallySelected = (system) => {
  const selectedInSystem = system.value.filter(satellite => selectedSatellites.value.includes(satellite));
  return selectedInSystem.length > 0 && selectedInSystem.length < system.value.length;
};

const getSelectedInSystem = (system) => {
  return system.value.filter(satellite => selectedSatellites.value.includes(satellite));
};

const toggleSystemSelection = (system) => {
  const isFullySelected = isSystemFullySelected(system);
  if (isFullySelected) {
    // 取消选择该系统的所有卫星
    selectedSatellites.value = selectedSatellites.value.filter(
      satellite => !system.value.includes(satellite)
    );
  } else {
    // 选择该系统的所有卫星
    const newSelections = system.value.filter(
      satellite => !selectedSatellites.value.includes(satellite)
    );
    selectedSatellites.value.push(...newSelections);
  }
};

const toggleSatellite = (satellite) => {
  const index = selectedSatellites.value.indexOf(satellite);
  if (index > -1) {
    selectedSatellites.value.splice(index, 1);
  } else {
    selectedSatellites.value.push(satellite);
  }
};

const selectAll = () => {
  selectedSatellites.value = satelliteData.value.flatMap(system => system.value);
};

const clearAll = () => {
  selectedSatellites.value = [];
};

const handleDialogClose = (done) => {
  done();
};

const confirmSelection = () => {
  console.log('选中的卫星:', selectedSatellites.value);
  showSatelliteDialog.value = false;
  // 这里可以添加确认选择后的逻辑
};

onMounted(async () => {
  await allSatelliteStore.initAllData();
  setAllsatellite(allSatelliteStore.data);
  HomeData.setChartfn("pseudoRange", getPseudoRangeInfo);
});
</script>

<style lang="scss" scoped>
.pseudo-range-info {
  .header-section {
    margin-bottom: 20px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .satellite-select-btn {
      background: #409eff;
      border: 1px solid #409eff;
      border-radius: 6px;
      padding: 10px 20px;
      font-size: 14px;
      font-weight: 500;
      color: white;
      transition: all 0.3s ease;

      &:hover {
        background: #337ecc;
        border-color: #337ecc;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(64, 158, 255, 0.3);
      }

      &:active {
        transform: translateY(0);
      }

      .selected-count {
        margin-left: 6px;
        background: rgba(255, 255, 255, 0.2);
        padding: 2px 6px;
        border-radius: 10px;
        font-size: 12px;
        font-weight: 600;
      }
    }
  }

  .chart-content {
    height: 420px;
    margin-bottom: 20px;
  }

  .tables-section {
    display: flex;
    gap: 20px;
    margin-top: 20px;

    .table-wrapper,
    .wrapper {
      background-color: #fff;
      border-radius: 4px;
      padding: 15px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .table-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #303133;
        padding-left: 5px;
        border-left: 4px solid #409eff;
      }

      .pagination-container {
        display: none;
      }
    }
    .table-wrapper {
      flex: 1;
    }
    .wrapper {
      width: 600px;
    }
  }
}

// 弹窗样式
:deep(.satellite-dialog) {
  .el-dialog__header {
    background: #f5f7fa;
    border-bottom: 1px solid #ebeef5;
    padding: 20px 24px;

    .el-dialog__title {
      color: #303133;
      font-weight: 600;
      font-size: 18px;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: #909399;
      font-size: 16px;

      &:hover {
        color: #409eff;
      }
    }
  }

  .el-dialog__body {
    padding: 0;
  }

  .el-dialog__footer {
    border-top: 1px solid #ebeef5;
    background: #fafafa;
    padding: 16px 24px;
  }
}

.dialog-content {
  .dialog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #ebeef5;
    background: #f8f9fa;

    .selection-summary {
      display: flex;
      gap: 16px;

      .total-count, .selected-summary {
        font-size: 14px;
        color: #606266;
      }

      .selected-summary {
        color: #409eff;
        font-weight: 500;
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }

  .satellite-systems {
    padding: 16px 24px 24px;

    :deep(.el-collapse) {
      border: none;

      .el-collapse-item {
        margin-bottom: 16px;
        border: 1px solid #ebeef5;
        border-radius: 8px;
        overflow: hidden;

        &:last-child {
          margin-bottom: 0;
        }

        .el-collapse-item__header {
          background: #f8f9fa;
          border: none;
          padding: 0;
          height: auto;
          line-height: normal;

          &:hover {
            background: #f0f2f5;
          }

          .el-collapse-item__arrow {
            margin-right: 12px;
            color: #909399;
          }
        }

        .el-collapse-item__wrap {
          border: none;

          .el-collapse-item__content {
            padding: 0;
          }
        }
      }
    }

    .collapse-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
      padding: 16px 20px;

      .system-info {
        display: flex;
        align-items: center;
        gap: 12px;

        .system-name {
          font-weight: 600;
          color: #303133;
          font-size: 15px;
        }

        .system-count {
          color: #909399;
          font-size: 13px;
        }
      }

      .selected-in-system {
        color: #409eff;
        font-size: 13px;
        font-weight: 500;
      }
    }

    .satellites-container {
      padding: 16px 20px 20px;
      background: white;

      .satellites-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
        gap: 8px;

        .satellite-item {
          display: flex;
          align-items: center;
          gap: 8px;
          padding: 8px 12px;
          border: 1px solid #dcdfe6;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          background: white;

          &:hover {
            border-color: #409eff;
            background: #ecf5ff;
          }

          &.selected {
            border-color: #409eff;
            background: #ecf5ff;

            .satellite-name {
              color: #409eff;
              font-weight: 500;
            }
          }

          .satellite-checkbox {
            margin: 0;
          }

          .satellite-name {
            font-size: 13px;
            color: #606266;
            user-select: none;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 24px;
}
</style>
