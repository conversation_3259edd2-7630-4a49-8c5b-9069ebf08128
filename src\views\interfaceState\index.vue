<template>
  <div class="interface-state">
    <div class="header-container">
      <h1 class="page-title">接口状态监控</h1>
      <!-- <div class="status-info">
        <span class="status-item">
          <i class="status-dot online"></i>在线设备: {{ getActiveDeviceCount() }}
        </span>
        <span class="status-item">
          <i class="status-dot offline"></i>离线设备: {{ getOfflineDeviceCount() }}
        </span>
        <span class="status-item">
          <i class="status-dot warning"></i>异常设备: {{ getWarningDeviceCount() }}
        </span>
      </div> -->
    </div>

    <!-- 添加选项卡 -->
    <div class="data-flow-tabs">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="工控机1" name="computer1"></el-tab-pane>
        <el-tab-pane label="工控机2" name="computer2"></el-tab-pane>
        <el-tab-pane label="卫星授时性能监测软件" name="satellite"></el-tab-pane>
      </el-tabs>
    </div>

    <div class="chart-container">
      <div class="legend">
        <div class="legend-item">
          <span class="line-normal"></span>
          <span class="text">正常连接</span>
        </div>
        <div class="legend-item">
          <span class="line-warning"></span>
          <span class="text">异常连接</span>
        </div>
        <div class="legend-item">
          <span class="line-error"></span>
          <span class="text">断开连接</span>
        </div>
      </div>
      <lineChart :data="currentExchangeData" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import lineChart from "@/components/lineChart/lineChart.vue";
import yinyong from "@/assets/device/yinyong.png";
import zhongx from "@/assets/device/zhongx.png";

// 添加选项卡数据
const activeTab = ref("computer1");

// 定义不同选项卡的设备数据
const tabsData = ref({
  computer1: {
    devices: [
      { name: "接收机1", status: "normal" },
      { name: "计数器1", status: "offline" },
      { name: "IGMAS", status: "warning" },
      { name: "FTP服务器", status: "normal" },
      { name: "频率分配放大器", status: "normal" },
      { name: "脉冲分配放大器", status: "warning" },
      { name: "自动气象站", status: "normal" },
    ]
  },
  computer2: {
    devices: [
      { name: "接收机2", status: "normal" },
      { name: "计数器2", status: "normal" },
      { name: "数据采集器", status: "warning" },
      { name: "FTP服务器", status: "normal" },
      { name: "频率分配放大器", status: "offline" },
      { name: "脉冲分配放大器", status: "normal" },
      { name: "自动气象站", status: "normal" },
    ]
  },
  satellite: {
    devices: [
      { name: "监测采集软件", status: "normal" },
      { name: "监测采集软件2", status: "warning" },
      { name: "监测数据采集预处理软件", status: "normal" },
      { name: "1603平台", status: "normal" },
      { name: "FTP服务器", status: "normal" },
      { name: "GNSS监测平台", status: "offline" },
    ]
  }
});

// 处理选项卡切换
function handleTabClick(tab) {
  console.log("选中选项卡:", tab.props.name);
  activeTab.value = tab.props.name;
}

// 获取当前选项卡的数据
const currentTabData = computed(() => {
  return tabsData.value[activeTab.value];
});

// 统计函数
function getActiveDeviceCount() {
  return currentTabData.value.devices.filter(item => item.status === "normal").length;
}

function getOfflineDeviceCount() {
  return currentTabData.value.devices.filter(item => item.status === "offline").length;
}

function getWarningDeviceCount() {
  return currentTabData.value.devices.filter(item => item.status === "warning").length;
}

// 根据选项卡动态生成图表数据
const currentExchangeData = computed(() => {
  const devices = currentTabData.value.devices;

  // 构建基本图表结构
  let exchangeData = {
    xAxis: {
      show: false,
      type: "value",
    },
    yAxis: {
      show: false,
      type: "value",
    },
    tooltip: {
      show: true,
      formatter: function(params) {
        if (params.data.status) {
          const statusColor = {
            'normal': '#52c41a',
            'warning': '#ff9800',
            'offline': '#f5222d'
          };
          const statusText = {
            'normal': '正常',
            'warning': '异常',
            'offline': '离线'
          };
          return `${params.name}<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${statusColor[params.data.status]};"></span>状态: ${statusText[params.data.status]}`;
        }
        return params.name;
      },
      backgroundColor: 'rgba(50,50,50,0.8)',
      borderColor: '#333',
      textStyle: {
        color: '#fff'
      },
      padding: 10,
      extraCssText: 'box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);'
    },
    series: [
      {
        type: "graph",
        zlevel: 5,
        draggable: false,
        coordinateSystem: "cartesian2d",
        label: {
          normal: {
            show: true,
            position: "bottom",
            backgroundColor: 'rgba(255,255,255,0.8)',
            padding: [4, 8],
            borderRadius: 4,
            color: '#333',
            fontWeight: 'bold'
          },
        },
        data: [
          {
            name: "eLORAN监测站总控平台",
            symbol: "image://" + zhongx,
            symbolSize: [220, 100],
            value: [400, 100],
            x: 400,
            y: 100,
          },
        ],
        lineStyle: {
          normal: {
            opacity: 1,
            color: "#53B5EA",
            curveness: 0,
            width: 2,
          },
        },
      }
    ]
  };

  // 添加设备节点
  for (let i = 0; i < devices.length; i++) {
    const device = devices[i];
    const xPos = 50 + i * 100;

    // 设备样式
    let itemStyle = undefined;
    if (device.status === 'warning') {
      itemStyle = {
        color: 'rgba(255, 152, 0, 0.2)',
        borderColor: '#ff9800',
        borderWidth: 2
      };
    } else if (device.status === 'offline') {
      itemStyle = {
        color: 'rgba(245, 34, 45, 0.2)',
        borderColor: '#f5222d',
        borderWidth: 2
      };
    }

    // 添加设备节点
    exchangeData.series[0].data.push({
      name: device.name,
      x: xPos,
      y: 400,
      value: [xPos, 400],
      symbol: "image://" + yinyong,
      symbolSize: [70, 67],
      status: device.status,
      itemStyle: itemStyle
    });
  }

  // 添加正常连接线
  const normalLines = {
    type: "lines",
    coordinateSystem: "cartesian2d",
    z: 1,
    zlevel: 2,
    animation: false,
    effect: {
      show: true,
      period: 4,
      trailLength: 0.2,
      symbolSize: 8,
      symbol: 'circle',
      loop: true,
      color: '#52c41a',
    },
          lineStyle: {
      normal: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [{
            offset: 0, color: '#1890ff'
          }, {
            offset: 1, color: '#52c41a'
          }],
        },
        width: 3,
        curveness: 0,
        shadowColor: 'rgba(82, 196, 26, 0.3)',
        shadowBlur: 10
      },
    },
    data: []
  };

  // 添加异常连接线
  const warningLines = {
    type: "lines",
    coordinateSystem: "cartesian2d",
    z: 1,
    zlevel: 3,
    animation: false,
    effect: {
      show: true,
      period: 3,
      trailLength: 0.2,
      symbolSize: 8,
      symbol: 'arrow',
      loop: true,
      color: '#ff9800',
    },
          lineStyle: {
      normal: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [{
            offset: 0, color: '#ff9800'
          }, {
            offset: 1, color: '#ffb74d'
          }],
        },
        width: 3,
        type: 'dashed',
        dashOffset: 8,
        dashArray: [6, 4],
        curveness: 0,
        shadowColor: 'rgba(255, 152, 0, 0.3)',
        shadowBlur: 15
      },
    },
    data: []
  };

  // 添加断开连接线
  const offlineLines = {
    type: "lines",
    coordinateSystem: "cartesian2d",
    z: 1,
    zlevel: 4,
    animation: false,
    effect: {
      show: false,
    },
          lineStyle: {
      normal: {
        color: {
          type: 'linear',
          x: 0, y: 0, x2: 0, y2: 1,
          colorStops: [{
            offset: 0, color: '#f5222d'
          }, {
            offset: 1, color: '#ff4d4f'
          }],
        },
        width: 2,
        type: 'dotted',
        dashOffset: 4,
        dashArray: [3, 6],
        curveness: 0,
        opacity: 0.8,
        shadowColor: 'rgba(245, 34, 45, 0.2)',
        shadowBlur: 10
      },
    },
    data: []
  };

  // 根据设备状态添加连接线
  for (let i = 0; i < devices.length; i++) {
    const device = devices[i];
    const xPos = 50 + i * 100;

    const lineCoords = [
      [xPos, 400],   // 设备位置
      [400, 100],    // 总控平台位置
    ];

    if (device.status === 'normal') {
      normalLines.data.push({ coords: lineCoords });
    } else if (device.status === 'warning') {
      warningLines.data.push({ coords: lineCoords });
    } else if (device.status === 'offline') {
      offlineLines.data.push({ coords: lineCoords });
    }
  }

  // 添加所有线条到图表
  exchangeData.series.push(normalLines);
  exchangeData.series.push(warningLines);
  exchangeData.series.push(offlineLines);

  return exchangeData;
});
</script>

<style scoped lang="scss">
.interface-state {
  width: 100%;
  height: 92vh;
  padding: 20px;
  background-color: #f5f7fa;

  .header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;

    .page-title {
      font-size: 24px;
      color: #303133;
      margin: 0;
      font-weight: 600;
      position: relative;
      padding-left: 12px;

      &:before {
        content: '';
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 4px;
        height: 20px;
        background: linear-gradient(to bottom, #1890ff, #52c41a);
        border-radius: 2px;
      }
    }

    .status-info {
      display: flex;
      gap: 20px;

      .status-item {
        display: flex;
        align-items: center;
        font-size: 14px;

        .status-dot {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          margin-right: 8px;

          &.online {
            background-color: #52c41a;
            box-shadow: 0 0 5px rgba(82, 196, 26, 0.5);
          }

          &.offline {
            background-color: #f5222d;
            box-shadow: 0 0 5px rgba(245, 34, 45, 0.5);
          }

          &.warning {
            background-color: #ff9800;
            box-shadow: 0 0 5px rgba(255, 152, 0, 0.5);
          }
        }
      }
    }
  }

  // 添加选项卡样式
  .data-flow-tabs {
    margin-bottom: 20px;

    :deep(.el-tabs__header) {
      margin-bottom: 15px;
    }

    :deep(.el-tabs__item) {
      font-size: 16px;
      padding: 0 20px;
      height: 40px;
      line-height: 40px;

      &.is-active {
        color: #3c95d5;
        font-weight: bold;
      }

      &:hover {
        color: #67c23a;
      }
    }

    :deep(.el-tabs__active-bar) {
      background-color: #3c95d5;
      height: 3px;
    }
  }

  .chart-container {
    position: relative;
    height: calc(100% - 120px); // 调整高度以适应选项卡
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    padding: 15px;
    overflow: hidden;

    .legend {
      position: absolute;
      top: 15px;
      right: 15px;
      background-color: rgba(255, 255, 255, 0.9);
      padding: 8px 12px;
      border-radius: 6px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      z-index: 10;
      display: flex;
      flex-direction: column;
      gap: 8px;

      .legend-item {
        display: flex;
        align-items: center;
        font-size: 12px;

        .text {
          margin-left: 8px;
        }

        .line-normal {
          display: inline-block;
          width: 25px;
          height: 3px;
          background: linear-gradient(to right, #1890ff, #52c41a);
        }

        .line-warning {
          display: inline-block;
          width: 25px;
          height: 3px;
          background: linear-gradient(to right, #ff9800, #ffb74d);
          border-top: 1px dashed #fff;
        }

        .line-error {
          display: inline-block;
          width: 25px;
          height: 3px;
          background: linear-gradient(to right, #f5222d, #ff4d4f);
          position: relative;
          border-top: 1px dotted #fff;

          &:before {
            content: '';
            position: absolute;
            left: 10px;
            top: -3px;
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #f5222d;
          }
        }
      }
    }
  }
}
</style>
