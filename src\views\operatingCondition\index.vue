<template>
  <div class="cabinet" ref="cabinet">
    <div
      :class="{
        cabinetData: true,
        'fade-in-left': initAniumation,
        'fade-out-left': !initAniumation,
      }"
      @mouseout="hideMagnified"
      @click.stop="clickMagnifiedImage($event, 'cabinet')"
    >
      <div class="cabinetName">
        <div class="name">{{ store.selectedCabinets.title }}</div>
        <div class="box">当前状态</div>
        <div
          class="status"
          :style="{ color: store.selectedCabinets.cabinetStatus?.color }"
        >
          {{ store.selectedCabinets.cabinetStatus?.title }}
        </div>
      </div>
      <div class="cabinetTabBox">
        <div
          v-for="item in adjustedList"
          :style="{
            bottom: `${item.adjustedBottom}px`, // 使用调整后的位置
            transition: 'bottom 0.3s ease', // 添加过渡动画
          }"
          :class="[
            {
              cabinetBox: true,
              cabinetBoxHover: mouseoverDataId == item.id,
              cabinetBoxClick: selectedData.id == item.id,
            },
            item.equipmentStatus.class,
          ]"
          :key="Date.now() + item.id"
          :data-id="item.id"
          @mouseover="showMagnified(item)"
          @mouseout="hideMagnified"
          @click.stop="clickMagnifiedImage(item, 'cabinetBox')"
        >
          {{ item.name }}
        </div>
      </div>
      <div
        class="cabinetImg"
        :style="{
          height: `${backHeight}px`,
          backgroundImage: `url(${Cabinet})`,
        }"
      >
        <img
          v-for="item in cabinetTab"
          :key="item.id"
          :src="item.img"
          :class="{
            cabinetImgItem: item.info,
            ImgItem: true,
            ['classID' + item.id]: true,
            mouseoverId: mouseoverDataId == item.id,
            cabinetBoxClick: selectedData.id == item.id,
          }"
          :data-id="item.id"
          :style="{
            height: `${imgHeight * item.usHeight}px`,
          }"
          :ref="item.info ? 'magnifiedImg' : ''"
          @mouseover="showMagnifiedImage(item, $event)"
          @mouseout="hideMagnifiedImage"
          @click.stop="clickMagnifiedImage(item, 'cabinetImgItem')"
        />
        <div
          v-if="magnifiedImage"
          class="magnifiedImage animate__zoomIn animate__animated"
          :style="magnifiedImageStyle"
        >
          <img :src="magnifiedImage" alt="Magnified Image" />
          <div class="describe">
            <div>资产名称：{{ magnifiedImageInfo.name }}</div>
            <div>资产编码：{{ magnifiedImageInfo.code }}</div>
            <div>资产类型：{{ magnifiedImageInfo.resourceTypeName }}</div>
            <!-- <div>状态：{{ magnifiedImageInfo?.equipmentStatus?.title }}</div> -->
          </div>
        </div>
      </div>
    </div>
    <div
      :class="{
        cabinetList: true,
        'fade-in-right': initAniumation,
        'fade-out-right': !initAniumation,
      }"
    >
      <div class="box" v-if="store.selectedCabinetsItem.info">
        <div class="cabinetListName">
          <span class="name">{{ store.selectedCabinetsItem.cabBox.name }}</span>
          <div
            :class="`status${store.selectedCabinetsItem?.cabBox?.equipmentStatus?.class}`"
          >
            {{ store.selectedCabinetsItem.cabBox.equipmentStatus?.title }}
          </div>
          <div style="margin-left: 10px">
            更新时间
            {{
              getDistanceFromNow(store.selectedCabinetsItem.cabBox.timestamp)
            }}
          </div>
        </div>
        <!-- {{ store.selectedCabinetsItem.fields }} -->
        <el-table
          class="tableHome"
          border
          :data="store.selectedCabinetsItem.fields"
        >
          <el-table-column
            property="name"
            align="center"
            label="工况名称"
          ></el-table-column>
          <el-table-column
            property="displayValue"
            align="center"
            label="工况状态"
          >
            <template #default="scope">
              <div class="status" v-if="scope.row.displayFormat == 'percent'">
                {{ Math.round(scope.row.displayValue * 100) + "%" }}
              </div>
              <div class="status" v-else>
                {{ scope.row.displayValue + (scope.row.unit || "") }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            property="displayValue"
            align="center"
            label="异常状态"
          >
            <template #default="scope">
              <div
                :class="{
                  statusnormal: !scope.row.errorStatus,
                  statusabnormal: scope.row.errorStatus,
                  status: true,
                }"
                v-if="'errorStatus' in scope.row"
              >
                {{ scope.row.errorStatus ? "异常" : "正常" }}
              </div>
              <div v-else class="status"></div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="box" v-else>
        <div class="cabinetListName">
          <span class="name">{{ store.selectedCabinets.title }}</span>
          <div
            :class="`status${store.selectedCabinets.equipmentStatus?.class}`"
          >
            {{ store.selectedCabinets.equipmentStatus?.title }}
          </div>

          <!-- 添加切换按钮 -->
          <div class="list-switch">
            <span
              :class="{ active: !showGlobalGroupList }"
              @click="showGlobalGroupList = false"
              >设备列表</span
            >
            <span
              :class="{ active: showGlobalGroupList }"
              @click="showGlobalGroupList = true"
              >分组列表</span
            >
          </div>
        </div>

        <!-- 设备列表 -->
        <el-table
          v-if="!showGlobalGroupList"
          class="tableHome"
          border
          :data="store.selectedCabinets?.allList"
        >
          <el-table-column
            property="name"
            width="200px"
            align="center"
            label="设备名称"
          />
          <el-table-column property="code" align="center" label="设备编码" />
          <el-table-column
            property="partSystem"
            align="center"
            label="分系统"
          />
          <el-table-column property="subSystem" align="center" label="子系统" />
          <el-table-column property="time" align="center" label="更新状态" />
          <el-table-column property="status" align="center" label="状态">
            <template #default="scope">
              <div
                :class="`status${scope.row.equipmentStatus.class}`"
                class="status"
              >
                {{ scope.row.equipmentStatus?.title }}
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分组列表 -->
        <el-table
          v-if="showGlobalGroupList"
          class="tableHome"
          border
          :data="formattedGroupData"
          :span-method="groupSpanMethod"
        >
          <el-table-column
            property="groupName"
            align="center"
            label="分组"
            min-width="120"
          ></el-table-column>
          <el-table-column
            property="name"
            align="center"
            label="设备名称"
            min-width="120"
          ></el-table-column>
          <el-table-column
            property="code"
            align="center"
            label="设备编码"
            min-width="120"
          ></el-table-column>
          <el-table-column
            property="status"
            align="center"
            label="状态"
            width="100"
          >
            <template #default="scope">
              <div
                :class="`status${scope.row.equipmentStatus?.class}`"
                class="status"
              >
                {{ scope.row.equipmentStatus?.title }}
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-card class="chartBox">
        <template #header>
          <div class="card-header">
            <span>工况统计</span>
          </div>
        </template>
        <lineChart class="lineChart" ref="lineChartBox" :data="lineChartData" />
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { useOperatingCondition } from "@/store/modules/operatingCondition";
import { ref, onMounted, computed, watch, onUnmounted } from "vue";
import { initWebSocket, unsubscribeAll, unsubscribe } from "@/api/ws";
import { ArrowLeft } from "@element-plus/icons-vue";
import Cabinet from "@/assets/device/机柜一.png";
import lineChart from "@/components/lineChart/lineChart.vue";
const store = useOperatingCondition();
const magnifiedImg = ref();
const initInfo = ref(false); // 优化是否进入就显示某一个东西
const cabinet = ref(); //图片的位置
const imgHeight = ref(0); // 每一个U 的高度
const backHeight = ref(0); // 背景的高度
const magnifiedImageInfo = ref({}); // 放大的图片信息
const cabinetTab = ref([]);
const magnifiedImage = ref(null); // 放大的图片
const magnifiedImageStyle = ref({}); // 放大图片的样式
const initAniumation = ref(true); // T 淡入 F 淡出
const selectedData = ref({ id: undefined }); // 选中的数据
const mouseoverDataId = ref(undefined); // 鼠标悬停的id
const lineChartBox = ref(); // 实例
const formattedGroupData = ref([]); // 格式化后的分组数据
const hasGroups = ref(false); // 是否有分组数据
const showGlobalGroupList = ref(false); // 控制全局视图下显示哪个列表

let time;
watch(
  () => store.selectedCabinets.lineChartData,
  (val) => {
    if (lineChartBox.value) {
      lineChartBox.value.getIns().setOption({
        series: [
          {
            data: val,
          },
        ],
        legend: {
          orient: "vertical", // 垂直排列
          top: "40%", // 垂直居中
          right: "100px", // 右边距
          formatter: function (name) {
            // 使用传入给图表的实际数据 val
            let data = val.find((item) => item.name === name);
            return data ? `${name} - ${data.value}条` : name;
          },
        },
      });
    }
  },
  { deep: true },
);
const clickMagnifiedImage = async (event, type) => {
  if (store.selectedCabinetsItem.cabBox.id) {
    unsubscribe(
      "/topic/gkInfo/resource/" + store.selectedCabinetsItem.cabBox.id,
    );
  }
  const objType = {
    // 最外面的点击
    cabinet: async (event) => {
      const targetId = event.target.dataset.id;
      if (targetId && targetId !== "9999") {
        const targetItem = cabinetTab.value.find((item) => item.id == targetId);
        if (targetItem.infoApi) {
          selectedData.value = targetItem;
        }
        // await store.handleSelect(event, "cabinetItem");
      } else {
        selectedData.value = { id: undefined };
        mouseoverDataId.value = undefined;
        store.handleBlank();
      }
    },
    // 左面板点击
    cabinetBox: async (event) => {
      selectedData.value = cabinetTab.value.find((item) => item.id == event.id);
      await store.handleSelect(event, "cabinetItem");
    },
    // 图片点击
    cabinetImgItem: async (event) => {
      console.log("store.selectedCabinetsItem", store.selectedCabinetsItem);
      if (event.id !== "9999" && event.infoApi) {
        selectedData.value = cabinetTab.value.find(
          (item) => item.id == event.id,
        );
        await store.handleSelect(event, "cabinetItem");
      }
    },
  };
  await objType[type](event);
};

const calculateAdjustedPositions = (devices) => {
  const sorted = [...devices].sort((a, b) => a.usStart - b.usStart);
  const positions = [];
  let prevBottom = -Infinity;
  const elementHeight = 30; // 每个元素固定高度
  const minSpacing = 1; // 最小间距

  sorted.forEach((device) => {
    const idealBottom = 17 * (device.usHeight / 2 + device.usStart);
    let adjustedBottom = idealBottom;
    const currentTop = adjustedBottom - elementHeight;
    if (currentTop < prevBottom) {
      adjustedBottom = prevBottom + elementHeight + minSpacing;
    }
    prevBottom = adjustedBottom;
    positions.push({
      ...device,
      adjustedBottom,
    });
  });
  return positions;
};

// 计算属性获取调整后的列表
const adjustedList = computed(() => {
  return calculateAdjustedPositions(store.selectedCabinets.allList || []);
});

onMounted(async () => {
  initWebSocket();
  await store.init();
  // 获取图片的高度
  backHeight.value = 850;
  imgHeight.value = 40;
  cabinetTab.value = store.selectedCabinets.newResources;
  initInfo.value = false;
  setTimeout(() => {
    initInfo.value = true;
  }, 1000);

  if (store.selectedCabinetsItem.info) {
    selectedData.value = cabinetTab.value.find(
      (item) => item.id == store.selectedCabinetsItem.cabBox.id,
    );
  }

  setList();

  // 处理分组数据
  formattedGroupData.value = processGroupData();

  // 监听数据变化重新处理分组
  watch(
    () => store.selectedCabinets?.allList,
    () => {
      formattedGroupData.value = processGroupData();
      setList();
    },
    { deep: true },
  );
  time = setInterval(() => {
    setList();
    store.selectedCabinetsItem.cabBox = {
      ...store.selectedCabinetsItem.cabBox,
    };
  }, 60000);
});

// 处理分组数据
const processGroupData = () => {
  if (!store.selectedCabinets?.allList) return [];

  // 处理有分组的数据
  const formattedData = [];

  // 第一步：获取所有唯一的分组标签
  const groupNames = new Set();
  const groupCountMap = {}; // 存储每个分组的项目数量

  store.selectedCabinets.allList.forEach((item) => {
    let groupName = "未分组";

    if (item.labels) {
      try {
        // 尝试解析 JSON 字符串
        const labelsArray = JSON.parse(item.labels);
        if (Array.isArray(labelsArray) && labelsArray.length > 0) {
          // 使用第一个标签作为分组名
          groupName = labelsArray[0];
        }
      } catch (e) {
        console.error("解析标签失败:", e);
      }
    }

    groupNames.add(groupName);

    // 计数每个分组内的项目数量
    if (!groupCountMap[groupName]) {
      groupCountMap[groupName] = 0;
    }
    groupCountMap[groupName]++;

    // 复制该项并添加分组名和索引信息
    const newItem = {
      ...item,
      groupName,
      groupIndex: groupCountMap[groupName] - 1,
    };
    formattedData.push(newItem);
  });

  // 设置hasGroups标志
  hasGroups.value = groupNames.size > 1 || !groupNames.has("未分组");

  // 按分组名称排序
  return formattedData.sort((a, b) => {
    // 首先按分组名称排序
    if (a.groupName !== b.groupName) {
      // 把"未分组"放到最后
      if (a.groupName === "未分组") return 1;
      if (b.groupName === "未分组") return -1;
      return a.groupName.localeCompare(b.groupName);
    }
    // 同一分组内保持原始顺序
    return 0;
  });
};

// 表格合并列方法
const groupSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  if (columnIndex === 0) {
    // 只处理第一列(分组列)
    // 如果是一个分组的第一行
    if (
      rowIndex === 0 ||
      formattedGroupData.value[rowIndex - 1].groupName !== row.groupName
    ) {
      // 获取当前分组在表格中占用的行数
      let rowspan = 0;
      let i = rowIndex;
      while (
        i < formattedGroupData.value.length &&
        formattedGroupData.value[i].groupName === row.groupName
      ) {
        rowspan++;
        i++;
      }
      return { rowspan, colspan: 1 };
    } else {
      // 其他行不显示分组名称
      return { rowspan: 0, colspan: 0 };
    }
  }
};

const setList = () => {
  if (store.selectedCabinets?.allList.length > 0) {
    store.selectedCabinets?.allList?.forEach((item) => {
      if (item.extendInfo?.gkUpdateTime) {
        item.time = getDistanceFromNow(item.extendInfo.gkUpdateTime);
      } else {
        item.time = "--";
      }
    });
  }
};

// 计算距离现在的时间
const getDistanceFromNow = (time) => {
  const now = new Date();
  const targetTime = new Date(time);
  const distance = now - targetTime;
  var minutes = Math.floor(distance / 60000);
  isNaN(minutes) && (minutes = 0);
  if (minutes <= 1) {
    return "1分钟前";
  }
  return `${minutes}分钟前`;
};

onUnmounted(() => {
  clearInterval(time);
  unsubscribeAll();
});

const showMagnified = (item) => {
  mouseoverDataId.value = item.id;
};
const hideMagnified = () => {
  mouseoverDataId.value = undefined;
};

const showMagnifiedImage = (item, event) => {
  if (!item.info) return;
  if (!initInfo.value) return;
  if (!item.infoApi) return;
  let url = item.img;
  const rect = event.target.getBoundingClientRect();
  mouseoverDataId.value = item.id;
  let top = rect.top - 100; // 垂直居中
  magnifiedImage.value = url;
  magnifiedImageInfo.value = item;
  magnifiedImageStyle.value = {
    position: "absolute",
    left: "420px", // 放大图片显示在左侧
    top: `${top}px`,
    // transform: "translateY(-50%)",
    border: "1px solid #ccc",
    borderRadius: "5px",
    boxShadow: "0 0 10px rgba(0, 0, 0, 0.5)",
    zIndex: 99,
    transform: "scale(2.6)",
  };
};

const hideMagnifiedImage = () => {
  magnifiedImage.value = null;
};
const lineChartData = {
  tooltip: {
    trigger: "item",
  },
  legend: {
    orient: "vertical", // 垂直排列
    top: "40%", // 垂直居中
    right: "100px", // 右边距
    formatter: function (name) {
      // 找到对应的 data 项
      let data = lineChartData.series[0].data.find(
        (item) => item.name === name,
      );
      return `${name} - ${data?.value || 0}条`;
    },
  },
  series: [
    {
      name: "设备",
      type: "pie",
      radius: ["40%", "60%"],
      center: ["40%", "50%"],
      label: {
        show: true,
        position: "outside",
        formatter: function (params) {
          // 检查百分比是否为 0
          if (params.percent > 0) {
            // 如果百分比大于 0，显示百分比
            return `${params.percent.toFixed(2)}%\n${params.name}--${
              params.value
            }条`;
          } else {
            // 如果百分比为 0，不显示百分比
            return `${params.name}--${params.value}条`;
          }
        },
        fontSize: 12,
      },
      labelLine: {
        show: true,
        length: 20,
        length2: 40,
      },
      itemStyle: {
        color: function (params) {
          var colorList = ["#06b6d4", "#fde047", "#f43f5e"];
          return colorList[params.dataIndex];
        },
      },
      data: store.selectedCabinets.lineChartData,
    },
  ],
};
</script>

<style scoped lang="scss">
.cabinet {
  width: 100%;
  height: 100%;
  display: flex;
  background-color: #f0f2f5;
  position: relative;
  overflow: hidden;
  .cabinetData {
    flex: 1;
    display: flex;
    position: relative;
    background: #fff;
    .cabinetImg {
      width: 302px;
      background-size: contain;
      background-repeat: no-repeat;
      background-position: center;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: space-around;
      position: relative;
      padding-top: 68px;
      padding-bottom: 68px;
      margin: auto 0px;
      .cabinetImgItem {
        transition: all 0.3s ease-in-out;
      }
      .ImgItem {
        width: 195px;
      }
      .cabinetImgFD {
        transform: scale(1.2);
        border: 2px solid #266fe8;
      }
      .magnifiedImage {
        position: absolute;
        width: 200px;
        height: auto;
        border: 1px solid #ccc;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
        padding: 6px;
        z-index: 10;
        background-color: rgba(237, 241, 213, 0.89);
      }
      .describe {
        font-size: 6px;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        padding: 5px;
        div {
          width: 60%;
        }
        div:nth-child(2n) {
          width: 40%;
          padding-left: 10px;
        }
      }
      .mouseoverId {
        transform: scale(1.1);
        border: 2px solid #1daf4e;
      }
      .cabinetBoxClick {
        transform: scale(1.3);
        border: 2px solid #266fe8;
      }
    }
    .cabinetName {
      position: absolute;
      top: 35px;
      left: 24px;
      z-index: 9;
      width: 222px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-family:
        PingFangSC,
        PingFang SC;
      font-weight: 600;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      .box {
        color: rgba(0, 0, 0, 0.45);
      }
      .status {
        font-weight: 400;
        font-size: 24px;
        color: #078d5c;
      }
    }
    .returnHome {
      position: absolute;
      top: 10px;
      left: 14px;
      z-index: 9;
      font-weight: 400;
      font-size: 14px;
      display: flex;
      align-items: center;
      line-height: 2px;
      cursor: pointer;
      .el-icon {
        margin-right: 2px;
        font-size: 14px;
      }
    }
  }

  .cabinetTabBox {
    position: relative;
    width: 250px;
    top: -25px;
    left: 24px;
    .cabinetBox {
      position: absolute;
      padding: 12px;
      width: 220px;
      height: 20px;
      border-radius: 2px;
      border: 1px solid #266fe8;
      padding-left: 30px;
      line-height: 0px;
      cursor: pointer;
      font-size: 14px;
    }
    .cabinetBox::after {
      content: "";
      position: absolute;
      top: 7px;
      right: -4px;
      width: 6px;
      height: 6px;
      border-top: 1px solid;
      border-left: 1px solid;
      border-color: #266fe8;
      background: #fff;
      transform: rotate(-225deg);
    }
    .cabinetBox::before {
      content: "";
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 12px;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #266fe8;
    }

    .cabinetBoxHover {
      background-color: #94d5aa;
      &::after {
        background-color: #94d5aa;
      }
    }
    .cabinetBoxClick {
      color: #fff;
      background-color: #266fe8;
      &::after {
        background-color: #266fe8;
      }
    }
    .abnormal {
      border-color: #c9353f;
      &::after {
        border-color: #c9353f;
      }
      &::before {
        background-color: #c9353f;
      }
    }
    .normal {
      border-color: #266fe8;
      &::after {
        border-color: #266fe8;
      }
      &::before {
        background-color: #266fe8;
      }
    }
    .fault {
      border-color: #fde047;
      &::after {
        border-color: #fde047;
      }
      &::before {
        background-color: #fde047;
      }
    }
  }

  .cabinetList {
    flex: 1;
    margin-left: 10px;
    background-color: #f0f2f5;
    display: flex;
    flex-direction: column;
    .box {
      display: flex;
      flex-direction: column;
      flex: 2;
      .cabinetListName {
        height: 40px;
        display: flex;
        align-items: center;
        padding: 0 12px;
        font-weight: 600;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        background-color: #fff;
        margin: 0 1px;
        .name {
          margin-right: 8px;
        }

        .list-switch {
          margin-left: auto;
          display: flex;
          border: 1px solid #dcdfe6;
          border-radius: 4px;
          overflow: hidden;

          span {
            padding: 2px 12px;
            font-size: 12px;
            cursor: pointer;
            background: #f5f7fa;
            font-weight: normal;

            &.active {
              background: #409eff;
              color: white;
            }

            &:first-child {
              border-right: 1px solid #dcdfe6;
            }
          }
        }
      }
      .tableHome {
        // flex: 1;
        font-family:
          PingFangSC,
          PingFang SC;
        font-weight: 400;
        font-size: 14px;

        .status {
          margin: 0 auto;
        }
        :deep(.el-table__row) {
          cursor: pointer;
        }
        :deep(.is-leaf) {
          background: #fafafa;
        }
        :deep(.el-scrollbar) {
          position: static !important;
        }

        :deep(.el-table__body-wrapper) {
          position: static !important;
        }
        :deep(.el-table__inner-wrapper) {
          position: static !important;
        }
      }
    }
    :deep(.el-table) {
      position: static !important;
      .el-table__cell {
        position: static !important;
      }
    }
    .chartBox {
      flex: 1;
      margin-top: 8px;
      background-color: #fff;
      display: flex;
      flex-direction: column;
      .card-header {
        height: 40px;
        padding: 10px 0px 8px 12px;
        font-weight: 600;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
      }
      :deep(.el-card__header) {
        padding: 0 !important;
      }
      :deep(.el-card__body) {
        flex: 1;
      }
      .lineChart {
        width: 100%;
        height: 214px;
        :deep(.vue-echarts-inner) {
          position: static !important;
          div {
            position: static !important;
          }
          canvas {
            position: static !important;
          }
        }
      }
    }
  }
}
.statusnormal {
  width: 42px;
  height: 24px;
  border: 1px solid #078d5c;
  text-align: center;
  line-height: 24px;
  border-radius: 3px;
  border: 1px solid #00a870;
  font-size: 13px;
  color: #00a870;
}

.statusabnormal {
  width: 42px;
  height: 24px;
  border: 1px solid #e34d59;
  text-align: center;
  line-height: 24px;
  border-radius: 3px;
  border: 1px solid #e34d59;
  font-size: 13px;
  color: #e34d59;
}
.statusfault {
  width: 42px;
  height: 24px;
  border: 1px solid #fde047;
  text-align: center;
  line-height: 24px;
  border-radius: 3px;
  border: 1px solid #fde047;
  font-size: 13px;
  color: #fde047;
}

// 媒体查询更具不同的高度来适配不同分辨率的屏幕
@media only screen and (max-height: 1080px) {
  .cabinet {
    .cabinetTabBox {
      top: -160px;
    }
  }
  .tableHome {
    height: 630px;
  }
}
@media only screen and (max-height: 919px) {
  .cabinet {
    .cabinetTabBox {
      top: -90px;
    }
  }
  .tableHome {
    height: 510px;
  }
}
</style>
