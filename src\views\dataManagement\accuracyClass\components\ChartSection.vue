<template>
  <div class="chart-section">
    <div class="chart-container">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="(BDS B2b PPP)时差" name="BDSB2bPPP">
          <BDSB2bPPP v-if="activeTab === 'BDSB2bPPP'" />
        </el-tab-pane>
        <el-tab-pane label="卫星伪码授时偏差" name="SatellitePseudocode">
          <SatellitePseudocode v-if="activeTab === 'SatellitePseudocode'" />
        </el-tab-pane>
        <el-tab-pane label="系统伪码授时偏差" name="SystemPseudos">
          <SystemPseudos v-if="activeTab === 'SystemPseudos'" />
        </el-tab-pane>
        <el-tab-pane label="系统载波相位授时偏差" name="SystemCarrierPhase">
          <SystemCarrierPhase v-if="activeTab === 'SystemCarrierPhase'" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import BDSB2bPPP from "./BDSB2bPPP.vue";
import SystemCarrierPhase from "./SystemCarrierPhase.vue";
import SatellitePseudocode from "./SatellitePseudocode.vue";
import SystemPseudos from "./SystemPseudos.vue";
const activeTab = ref("BDSB2bPPP");
</script>

<style lang="scss" scoped>
.chart-section {
  // padding: 20px;

  .chart-container {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    height: 91vh;
  }
}
</style>
