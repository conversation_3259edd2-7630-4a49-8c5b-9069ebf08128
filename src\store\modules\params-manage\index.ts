import { SetupStoreId } from '@/enum';
import { defineStore } from 'pinia';
import { reactive, toRefs } from 'vue';

export const useParamsManage = defineStore(
  SetupStoreId.ParamsManage,
  () => {
    // states
    const data = reactive({
      foundation: {
        autoRun: true,
        siteName: '',
        pwd: true,
        sreenLockWaitTime: 20,
        sreenLockPwd: '123'
      },
      siteList: [
        { name: '敦煌授时监测站', value: '敦煌授时监测站' },
        { name: '敦煌授时监测站', value: '敦煌授时监测站' },
        { name: '库尔勒授时监测站', value: '库尔勒授时监测站' },
        { name: '拉萨授时监测站', value: '拉萨授时监测站' },
        { name: '敦煌监测中心站', value: '敦煌监测中心站' },
      ],
      fromList: [
        {
          label: '通信参数',
          btn: false,
          children: [
            {
              label: '本地服务',
              prop: 'localIp',
              value: '***********:8080',
              disabled: true,
            },
            {
              label: '数据分析与处理子系统',
              prop: 'dataAnalysisIp',
              value: '***********:8080',
              disabled: false,
            },
            {
              label: '监测运控子系统',
              prop: 'monitorIp',
              value: '***********:8080',
              disabled: false,
            },


            {
              label: '远程配送云平台',
              prop: 'remoteDeliveryIp',
              value: '***********:8080',
              disabled: false,
            },
            {
              label: 'GNSS系统时差监测平台',
              prop: 'gnssIp',
              value: '***********:8080',
              disabled: false,
            },

            {
              label: '1603授时监测系统',
              prop: 'oneSixZeroThreeIp',
              value: '***********:8080',
              disabled: false,
            },
            {
              label: 'FTP服务器',
              prop: 'ftpIp',
              value: '***********:8080',
              disabled: false,
            },

            {
              label: '数据库连接地址',
              prop: 'connectAddress',
              value: '',
              disabled: false,
            },


          ],
        },

      ],
    });
    // getters

    // actions

    return {
      ...toRefs(data),
    };
  },
  {
    // 是否持久化到浏览器缓存localStorage
    persist: false,
  }
);
