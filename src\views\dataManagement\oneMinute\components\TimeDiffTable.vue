<template>
  <el-table :data="data" border style="width: 100%" height="300">
    <el-table-column prop="time" label="时间" width="180" />
    <el-table-column prop="BDS" label="BDS (ns)" />
    <el-table-column prop="GPS" label="GPS (ns)" />
    <el-table-column prop="GLONASS" label="GLONASS (ns)" />
    <el-table-column prop="GALILEO" label="GALILEO (ns)" />
  </el-table>
</template>

<script setup>
defineProps({
  data: {
    type: Array,
    required: true
  }
});
</script>
