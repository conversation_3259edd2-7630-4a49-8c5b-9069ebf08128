/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  alarmlist: () => import("@/views/alarmList/index.vue"),
  assessmentmanager_eop: () => import("@/views/assessmentManager/EOP/index.vue"),
  assessmentmanager_ggto: () => import("@/views/assessmentManager/GGTO/index.vue"),
  assessmentmanager_utco: () => import("@/views/assessmentManager/UTCO/index.vue"),
  assessmentmanager_timingperformanceevaluation: () => import("@/views/assessmentManager/timingperformanceEvaluation/index.vue"),
  datamanagement_accuracyclass: () => import("@/views/dataManagement/accuracyClass/index.vue"),
  datamanagement_fusion: () => import("@/views/dataManagement/fusion/index.vue"),
  datamanagement_gnssmonitor: () => import("@/views/dataManagement/gnssMonitor/index.vue"),
  datamanagement_navigation: () => import("@/views/dataManagement/navigation/index.vue"),
  datamanagement_oneminute: () => import("@/views/dataManagement/oneMinute/index.vue"),
  datamanagement_platform1603: () => import("@/views/dataManagement/platform1603/index.vue"),
  datamanagement_rawtimediff_b2b: () => import("@/views/dataManagement/rawTimeDiff/B2b/index.vue"),
  datamanagement_rawtimediff_bds: () => import("@/views/dataManagement/rawTimeDiff/BDS/index.vue"),
  datamanagement_rawtimediff_eop: () => import("@/views/dataManagement/rawTimeDiff/EOP/index.vue"),
  datamanagement_rawtimediff_ggto: () => import("@/views/dataManagement/rawTimeDiff/GGTO/index.vue"),
  datamanagement_rawtimediff_utco: () => import("@/views/dataManagement/rawTimeDiff/UTCO/index.vue"),
  datamanagement_transceiver: () => import("@/views/dataManagement/transceiver/index.vue"),
  filemanagement: () => import("@/views/fileManagement/index.vue"),
  help: () => import("@/views/help/index.vue"),
  home: () => import("@/views/home/<USER>"),
  interfacestate: () => import("@/views/interfaceState/index.vue"),
  log: () => import("@/views/log/index.vue"),
  operatingcondition: () => import("@/views/operatingCondition/index.vue"),
  "params-manage": () => import("@/views/params-manage/index.vue"),
};
