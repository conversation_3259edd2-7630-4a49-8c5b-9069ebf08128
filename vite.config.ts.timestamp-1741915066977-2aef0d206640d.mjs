// vite.config.ts
import process4 from "node:process";
import { URL, fileURLToPath } from "node:url";
import { defineConfig, loadEnv } from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/vite@5.3.1_@types+node@20.14.8_sass@1.77.6/node_modules/vite/dist/node/index.js";

// build/plugins/index.ts
import vue from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/@vitejs+plugin-vue@5.0.5_vite@5.3.1_@types+node@20.14.8_sass@1.77.6__vue@3.4.30_typescript@5.5.2_/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import vueJsx from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/@vitejs+plugin-vue-jsx@4.0.0_vite@5.3.1_@types+node@20.14.8_sass@1.77.6__vue@3.4.30_typescript@5.5.2_/node_modules/@vitejs/plugin-vue-jsx/dist/index.mjs";
import VueDevtools from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/vite-plugin-vue-devtools@7.3.4_rollup@4.18.0_vite@5.3.1_@types+node@20.14.8_sass@1.77.6__vue@3.4.30_typescript@5.5.2_/node_modules/vite-plugin-vue-devtools/dist/vite.mjs";
import progress from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/vite-plugin-progress@0.0.7_vite@5.3.1_@types+node@20.14.8_sass@1.77.6_/node_modules/vite-plugin-progress/dist/index.mjs";

// build/plugins/router.ts
import ElegantVueRouter from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/@elegant-router+vue@0.3.7/node_modules/@elegant-router/vue/dist/vite.mjs";
function setupElegantRouter() {
  return ElegantVueRouter({
    layouts: {
      base: "src/layouts/base-layout/index.vue",
      blank: "src/layouts/blank-layout/index.vue"
    },
    customRoutes: {
      names: [
        "exception_403",
        "exception_404",
        "exception_500",
        "document_project",
        "document_project-link",
        "document_vue",
        "document_vite",
        "document_naive",
        "document_antd"
      ]
    },
    routePathTransformer(routeName, routePath) {
      const key = routeName;
      if (key === "login") {
        const modules = ["pwd-login", "code-login", "register", "reset-pwd", "bind-wechat"];
        const moduleReg = modules.join("|");
        return `/login/:module(${moduleReg})?`;
      }
      return routePath;
    },
    onRouteMetaGen(routeName) {
      const key = routeName;
      const constantRoutes = ["login", "403", "404", "500"];
      const meta = {
        title: key
      };
      if (constantRoutes.includes(key)) {
        meta.constant = true;
      }
      return meta;
    }
  });
}

// build/plugins/unocss.ts
import process2 from "node:process";
import path from "node:path";
import unocss from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/@unocss+vite@0.61.0_rollup@4.18.0_vite@5.3.1_@types+node@20.14.8_sass@1.77.6_/node_modules/@unocss/vite/dist/index.mjs";
import presetIcons from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/@unocss+preset-icons@0.61.0/node_modules/@unocss/preset-icons/dist/index.mjs";
import { FileSystemIconLoader } from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/@iconify+utils@2.1.25/node_modules/@iconify/utils/lib/loader/node-loaders.mjs";
function setupUnocss(viteEnv) {
  const { VITE_ICON_PREFIX, VITE_ICON_LOCAL_PREFIX } = viteEnv;
  const localIconPath = path.join(process2.cwd(), "src/assets/svg-icon");
  const collectionName = VITE_ICON_LOCAL_PREFIX.replace(`${VITE_ICON_PREFIX}-`, "");
  return unocss({
    presets: [
      presetIcons({
        prefix: `${VITE_ICON_PREFIX}-`,
        scale: 1,
        extraProperties: {
          display: "inline-block"
        },
        collections: {
          [collectionName]: FileSystemIconLoader(
            localIconPath,
            (svg) => svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')
          )
        },
        warn: true
      })
    ]
  });
}

// build/plugins/unplugin.ts
import process3 from "node:process";
import path2 from "node:path";
import Icons from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/unplugin-icons@0.19.0_@vue+compiler-sfc@3.4.30_vue-template-compiler@2.7.16/node_modules/unplugin-icons/dist/vite.js";
import IconsResolver from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/unplugin-icons@0.19.0_@vue+compiler-sfc@3.4.30_vue-template-compiler@2.7.16/node_modules/unplugin-icons/dist/resolver.js";
import Components from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/unplugin-vue-components@0.27.0_@babel+parser@7.24.7_rollup@4.18.0_vue@3.4.30_typescript@5.5.2_/node_modules/unplugin-vue-components/dist/vite.js";
import { AntDesignVueResolver, NaiveUiResolver } from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/unplugin-vue-components@0.27.0_@babel+parser@7.24.7_rollup@4.18.0_vue@3.4.30_typescript@5.5.2_/node_modules/unplugin-vue-components/dist/resolvers.js";
import { FileSystemIconLoader as FileSystemIconLoader2 } from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/unplugin-icons@0.19.0_@vue+compiler-sfc@3.4.30_vue-template-compiler@2.7.16/node_modules/unplugin-icons/dist/loaders.js";
import { createSvgIconsPlugin } from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/vite-plugin-svg-icons@2.0.1_vite@5.3.1_@types+node@20.14.8_sass@1.77.6_/node_modules/vite-plugin-svg-icons/dist/index.mjs";
function setupUnplugin(viteEnv) {
  const { VITE_ICON_PREFIX, VITE_ICON_LOCAL_PREFIX } = viteEnv;
  const localIconPath = path2.join(process3.cwd(), "src/assets/svg-icon");
  const collectionName = VITE_ICON_LOCAL_PREFIX.replace(`${VITE_ICON_PREFIX}-`, "");
  const plugins = [
    Icons({
      compiler: "vue3",
      customCollections: {
        [collectionName]: FileSystemIconLoader2(
          localIconPath,
          (svg) => svg.replace(/^<svg\s/, '<svg width="1em" height="1em" ')
        )
      },
      scale: 1,
      defaultClass: "inline-block"
    }),
    Components({
      dts: "src/typings/components.d.ts",
      types: [{ from: "vue-router", names: ["RouterLink", "RouterView"] }],
      resolvers: [
        AntDesignVueResolver({
          importStyle: false
        }),
        NaiveUiResolver(),
        IconsResolver({ customCollections: [collectionName], componentPrefix: VITE_ICON_PREFIX })
      ]
    }),
    createSvgIconsPlugin({
      iconDirs: [localIconPath],
      symbolId: `${VITE_ICON_LOCAL_PREFIX}-[dir]-[name]`,
      inject: "body-last",
      customDomId: "__SVG_ICON_LOCAL__"
    })
  ];
  return plugins;
}

// build/plugins/html.ts
function setupHtmlPlugin(buildTime) {
  const plugin = {
    name: "html-plugin",
    apply: "build",
    transformIndexHtml(html) {
      return html.replace("<head>", `<head>
    <meta name="buildTime" content="${buildTime}">`);
    }
  };
  return plugin;
}

// build/plugins/index.ts
function setupVitePlugins(viteEnv, buildTime) {
  const plugins = [
    vue({
      script: {
        defineModel: true
      }
    }),
    vueJsx(),
    VueDevtools(),
    setupElegantRouter(),
    setupUnocss(viteEnv),
    ...setupUnplugin(viteEnv),
    progress(),
    setupHtmlPlugin(buildTime)
  ];
  return plugins;
}

// build/config/time.ts
import dayjs from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/dayjs.min.js";
import utc from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/plugin/utc.js";
import timezone from "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/node_modules/.pnpm/dayjs@1.11.11/node_modules/dayjs/plugin/timezone.js";
function getBuildTime() {
  dayjs.extend(utc);
  dayjs.extend(timezone);
  const buildTime = dayjs.tz(Date.now(), "Asia/Shanghai").format("YYYY-MM-DD HH:mm:ss");
  return buildTime;
}

// src/api/config/index.js
var proxySource = {
  "/Api": "http://192.168.111.15:8080"
  // 外网测试环境
  // htpp
};
var proxyRoot = [];
for (let key in proxySource) {
  proxyRoot.push(
    process.env.NODE_ENV === "production" ? proxySource[key] : key + "/"
  );
}
var config_default = {
  prefix: "temp_",
  //项目前缀
  xAppVersion: "1.0.0",
  //版本
  base_path: "",
  //  项目地址
  proxySource,
  //代理源配置
  root: proxyRoot,
  // 配置API接口地址
  proxyUrl: {
    defult: ""
  },
  publicKey: "LTAI4GKXdB0hcaFS78629b526a153b17",
  //app 加密密钥
  publicKeyRecharge: "jstxbetcUroad123",
  //圈存加密密钥
  publicKeyMd5Recharge: "LTAI4GKXdB0hcaFS78629b526a153b17",
  isTest: false
  //是否测试环境
};

// vite.config.ts
var __vite_injected_original_import_meta_url = "file:///D:/jnx/%E6%98%9F%E5%9C%B0-%E5%8D%AB%E6%98%9F%E6%8E%88%E6%97%B6%E6%80%A7%E8%83%BD%E7%9B%91%E6%B5%8B%E8%BD%AF%E4%BB%B6/vite.config.ts";
var proxy = {};
for (let key in config_default.proxySource) {
  proxy[key] = {
    target: config_default.proxySource[key],
    changeOrigin: true,
    rewrite: (path3) => path3.replace(`${key}`, "")
  };
}
console.log(proxy);
var vite_config_default = defineConfig((configEnv) => {
  const viteEnv = loadEnv(configEnv.mode, process4.cwd());
  const buildTime = getBuildTime();
  return {
    base: viteEnv.VITE_BASE_URL,
    resolve: {
      alias: {
        "~": fileURLToPath(new URL("./", __vite_injected_original_import_meta_url)),
        "@": fileURLToPath(new URL("./src", __vite_injected_original_import_meta_url))
      }
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "./src/styles/scss/global.scss" as *;`
        }
      }
    },
    plugins: setupVitePlugins(viteEnv, buildTime),
    define: {
      BUILD_TIME: JSON.stringify(buildTime)
    },
    // server: {
    //   host: '0.0.0.0',
    //   port: 9527,
    //   open: true,
    //   proxy: createViteProxy(viteEnv, configEnv.command === 'serve'),
    //   fs: {
    //     cachedChecks: false
    //   }
    // },
    server: {
      proxy
    },
    preview: {
      port: 9725
    },
    build: {
      reportCompressedSize: false,
      sourcemap: viteEnv.VITE_SOURCE_MAP === "Y",
      commonjsOptions: {
        ignoreTryCatch: false
      }
    }
  };
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,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
