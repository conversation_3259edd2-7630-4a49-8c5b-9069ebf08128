<template>
  <div class="chart-section">
    <div class="chart-container">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="卫星伪码授时偏差" name="pseudoRange">
          <pseudo-range-info v-if="activeTab === 'pseudoRange'" />
        </el-tab-pane>
        <el-tab-pane label="系统伪码授时偏差" name="fusion">
          <fusion-info v-if="activeTab === 'fusion'" />
        </el-tab-pane>
        <el-tab-pane label="卫星伪码时差" name="systemPseudo">
          <satellitePseudocode v-if="activeTab === 'systemPseudo'" />
        </el-tab-pane>
        <el-tab-pane label="系统伪码时差" name="systemPseudo1">
          <system-pseudo-time-diff v-if="activeTab === 'systemPseudo1'" />
        </el-tab-pane>
        <el-tab-pane label="系统载波相位时差" name="carrierPhaseDiff">
          <carrier-phase-diff v-if="activeTab === 'carrierPhaseDiff'" />
        </el-tab-pane>
        <el-tab-pane label="系统载波相位授时偏差" name="carrierPhase">
          <carrier-phase-bias v-if="activeTab === 'carrierPhase'" />
        </el-tab-pane>
        <el-tab-pane label="BDS B2b PPP时差" name="b2bPPP">
          <B2bPPPDiff v-if="activeTab === 'b2bPPP'" />
        </el-tab-pane>
        <!-- <el-tab-pane label="最终精密PPP时差" name="accuracy">
          <satellite-accuracy />
        </el-tab-pane> -->
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import FusionInfo from './FusionInfo.vue';
import PseudoRangeInfo from './PseudoRangeInfo.vue';
import SystemPseudoTimeDiff from './SystemPseudoTimeDiff.vue';
import CarrierPhaseBias from './CarrierPhaseBias.vue';
import CarrierPhaseDiff from './CarrierPhaseDiff.vue';
import B2bPPPDiff from './B2bPPPDiff.vue';
import satellitePseudocode from './satellitePseudocode.vue';
import SatelliteAccuracy from './SatelliteAccuracy.vue';

const activeTab = ref("pseudoRange");
</script>

<style lang="scss" scoped>
.chart-section {
  // padding: 20px;

  .chart-container {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    height: 91vh;
  }
}
</style>
