<template>
  <div class="eop-container">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="数据来源">
          <el-select
            style="width: 160px"
            v-model="searchForm.dataSource"
            placeholder="请选择数据来源"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker
            style="width: 300px"
            v-model="searchForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :disabled-date="configure.disabledDate"
            :shortcuts="configure.dateShortcuts"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch" :loading="loading">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      class="table-border"
      border
      style="width: 100%"
      v-loading="loading"
      :header-cell-style="headerCellStyle"
      :cell-style="cellStyle"
    >
    <el-table-column type="index" label="序号" width="80" />
      <el-table-column prop="timestamp" label="时间" width="180" />
      <el-table-column prop="resourceTypeName" label="数据来源" width="280">
        <template #default="scope">
          {{
            scope.row.resourceTypeName + `(${scope.row.resourceInstanceCode})`
          }}
        </template>
      </el-table-column>
      <el-table-column prop="wnot" label="GPST周计数" />
      <el-table-column prop="tot" label="GPST周内秒" />
      <el-table-column prop="a0" label="A0(s)">
        <template #default="scope">
          {{ toScientificNotation(scope.row.a0) }}
        </template>
      </el-table-column>
      <el-table-column prop="a1" label="A1(s/s)">
        <template #default="scope">
          {{ toScientificNotation(scope.row.a1) }}
        </template>
      </el-table-column>
      <el-table-column prop="a2" label="A2(s/s²)">
        <template #default="scope">
          {{ toScientificNotation(scope.row.a2) }}
        </template>
      </el-table-column>
      <el-table-column prop="deltaLs" label="ΔtLS(s)" />
      <el-table-column prop="wnLsf" label="WNLSF" />
      <el-table-column prop="dnLsf" label="DNLSF" />
      <el-table-column prop="deltaLsf" label="ΔtLSF(s)" />
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[20, 30, 50]"
        layout="sizes, total, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import configure from "@/utils/configure.js";
import apiAjax from "@/api/index";
// 将数字转换为科学计数法
const toScientificNotation = (numStr) => {
  const num = parseFloat(numStr);
  return num.toExponential(4);
};
const gnssIdList = {
  1: "GPS",
  2: "GLONASS",
  3: "Galileo",
};

// 表头样式
const headerCellStyle = {
  background: "#f5f7fa",
  color: "#606266",
  fontWeight: "bold",
  fontSize: "14px",
  height: "50px",
};

// 单元格样式
const cellStyle = {
  fontSize: "16px",
  padding: "10px 0",
};

// API路径
const apiUrl =
  "/api/jnx/fusion/apps/satellite/collect/elecParam/utco/findUtcoGpscnavPage";

// 搜索表单
const searchForm = reactive({
  timeRange: [],
  dataSource: "",
});

const typeOptions = [
  { label: "全部", value: "" },
  { label: "SAM1", value: "SAM1" },
  { label: "SAM2", value: "SAM2" },
  { label: "SAM3", value: "SAM3" },
];

// 分页相关
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const loading = ref(false);

// 表格数据
const tableData = ref([]);

// 构建查询参数
const setUrlQuery = () => {
  const postData = {
    ...searchForm,
    page: currentPage.value,
    size: pageSize.value,
  };

  // 处理时间范围
  if (searchForm.timeRange?.length === 2) {
    postData.beginTime = configure.formatDate(
      new Date(`${searchForm.timeRange[0]} 00:00:00`),
    );
    postData.endTime = configure.formatDate(
      new Date(`${searchForm.timeRange[1]} 23:59:59`),
    );
  }
  delete postData.timeRange;

  // 构建查询字符串
  return Object.entries(postData)
    .filter(
      ([_, value]) => value !== undefined && value !== null && value !== "",
    )
    .map(([key, value]) => {
      // 时间相关参数不进行URL编码，其他参数正常编码
      if (key === "beginTime" || key === "endTime") {
        return `${encodeURIComponent(key)}=${value}`;
      } else {
        return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
      }
    })
    .join("&");
};

// 查询数据
const onSearch = async () => {
  loading.value = true;
  try {
    const params = setUrlQuery();
    let { data } = await apiAjax.get(`${apiUrl}?${params}`);
    tableData.value = data.content || [];
    total.value = data.totalElements || 0;
  } catch (error) {
    console.error("查询数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 分页方法
const handleSizeChange = (val) => {
  currentPage.value = 1;
  pageSize.value = val;
  onSearch();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  onSearch();
};

onMounted(async () => {
  onSearch();
});
</script>

<style scoped lang="scss">
.eop-container {
  background: #fff;

  .page-title {
    margin-bottom: 16px;
    h3 {
      font-size: 18px;
      font-weight: bold;
      color: #303133;
    }
  }

  .search-area {
    margin-top: 16px;
    margin-bottom: 16px;
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
@media only screen and (max-height: 1080px) {
  .table-border {
    height: 760px;
  }
}
@media only screen and (max-height: 919px) {
  .table-border {
    height: 600px;
  }
}
</style>
