{"name": "soybean-admin", "type": "module", "version": "1.2.6", "description": "A fresh and elegant admin template, based on Vue3、Vite3、TypeScript、NaiveUI and UnoCSS. 一个基于Vue3、Vite3、TypeScript、NaiveUI and UnoCSS的清新优雅的中后台模版。", "author": {"name": "Soybean", "email": "<EMAIL>", "url": "https://github.com/soybeanjs"}, "license": "MIT", "homepage": "https://github.com/soybeanjs/soybean-admin", "repository": {"url": "https://github.com/soybeanjs/soybean-admin.git"}, "bugs": {"url": "https://github.com/soybeanjs/soybean-admin/issues"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite5", "TypeScript", "naive-ui", "naive-ui-admin", "ant-design-vue v4", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"build": "vite build --mode prod", "build:mem": "node --max_old_space_size=8192 node_modules/vite/bin/vite.js build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "dev": "vite --mode prod", "dev:test": "vite --mode test", "dev:yun": "vite --mode yun", "gen-route": "sa gen-route", "lint": "eslint . --fix", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "update-pkg": "sa update-pkg", "mycli": "node ./bin/cli.js"}, "dependencies": {"@better-scroll/core": "2.5.1", "@element-plus/icons-vue": "^2.0.9", "@iconify/vue": "4.1.2", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@vueuse/core": "10.11.0", "autofit.js": "^3.2.8", "axios": "1.7.2", "clipboard": "2.0.11", "dayjs": "1.11.11", "echarts": "5.5.0", "element-plus": "^2.2.14", "lodash-es": "4.17.21", "naive-ui": "2.38.2", "nprogress": "0.2.0", "pinia": "2.1.7", "pinia-plugin-persistedstate": "^3.1.0", "swiper": "^10.3.1", "tailwind-merge": "2.3.0", "vue": "3.4.30", "vue-draggable-plus": "0.5.0", "vue-echarts": "^6.7.3", "vue-i18n": "9.13.1", "vue-router": "4.4.0"}, "devDependencies": {"@elegant-router/vue": "0.3.7", "@iconify/json": "2.2.221", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.3.7", "@types/lodash-es": "4.17.12", "@types/node": "20.14.8", "@types/nprogress": "0.2.3", "@unocss/eslint-config": "0.61.0", "@unocss/preset-icons": "0.61.0", "@unocss/preset-uno": "0.61.0", "@unocss/transformer-directives": "0.61.0", "@unocss/transformer-variant-group": "0.61.0", "@unocss/vite": "0.61.0", "@vitejs/plugin-vue": "5.0.5", "@vitejs/plugin-vue-jsx": "4.0.0", "commander": "^12.1.0", "eslint": "9.5.0", "eslint-plugin-vue": "9.26.0", "fs-extra": "^11.2.0", "lint-staged": "15.2.7", "sass": "1.77.6", "simple-git-hooks": "2.11.1", "tsx": "4.15.7", "typescript": "5.5.2", "unplugin-icons": "0.19.0", "unplugin-vue-components": "0.27.0", "vite": "5.3.1", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.3.4", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.0.22"}, "simple-git-hooks": {"commit-msg": "", "pre-commit": ""}, "lint-staged": {"*": "eslint --fix"}, "website": "https://admin.soybeanjs.cn"}