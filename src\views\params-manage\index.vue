<template>
  <div class="con">
    <div class="card-con">
      <el-card class="card cardBox">
        <template #header>
          <div class="card-header">
            <span class="header-title">
              文件存储
              <span v-if="currentStoragePath" class="current-path-display">
                (当前: {{ currentStoragePath }})
              </span>
            </span>
            <el-button
              type="primary"
              :icon="loadingTree ? 'el-icon-loading' : EditPen"
              :loading="loadingTree"
              @click="togglePathTreeVisibility"
              >{{ showPathTree ? "隐藏路径选择" : "修改地址" }}</el-button
            >
          </div>
        </template>

        <div class="main-content-area">
          <!-- Section 1: Path History List -->
          <div class="path-history-container">
            <h3 class="section-title">存储历史</h3>
            <div class="path-history">
              <el-table
                :data="pathHistoryList"
                stripe
                border
                height="calc(100vh - 300px)"
                width="100%"
                class="path-table"
                :header-cell-style="{
                  background: '#fafafa',
                  color: '#606266',
                  fontWeight: 'bold',
                }"
              >
                <el-table-column
                  prop="fullPath"
                  label="存储路径"
                  align="center"
                  min-width="250"
                />
                <el-table-column
                  prop="operateTime"
                  label="更新时间"
                  align="center"
                  width="180"
                />
                <el-table-column
                  prop="operateUser"
                  label="操作人"
                  align="center"
                  width="150"
                />
                <el-table-column
                  prop="status"
                  label="状态"
                  align="center"
                  width="120"
                >
                  <template #default="scope">
                    <el-tag
                      :type="getStatusTagType(scope.row.status)"
                      effect="light"
                    >
                      {{ getStatusText(scope.row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" align="center" width="260">
                  <template #default="scope">
                    <el-tooltip
                      content="将此路径设为当前使用的存储路径"
                      placement="top"
                      :enterable="false"
                    >
                      <el-button
                        v-if="scope.row.status === STATUS_CODES.HISTORY"
                        :icon="Check"
                        type="primary"
                        size="small"
                        @click="handleSetCurrent(scope.row)"
                        aria-label="设为当前"
                      >
                        设为当前
                      </el-button>
                    </el-tooltip>
                    <el-tooltip
                      content="作废此路径，之后将无法选用"
                      placement="top"
                      :enterable="false"
                    >
                      <el-button
                        v-if="scope.row.status !== STATUS_CODES.DEACTIVATED"
                        :icon="Delete"
                        type="danger"
                        size="small"
                        plain
                        @click="handleDeactivate(scope.row)"
                        aria-label="作废"
                      >
                        作废
                      </el-button>
                    </el-tooltip>
                    <el-tooltip
                      content="恢复此路径为历史路径"
                      placement="top"
                      :enterable="false"
                    >
                      <el-button
                        v-if="scope.row.status === STATUS_CODES.DEACTIVATED"
                        :icon="RefreshLeft"
                        type="warning"
                        size="small"
                        plain
                        @click="handleRestore(scope.row)"
                        aria-label="恢复"
                      >
                        恢复
                      </el-button>
                    </el-tooltip>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- Section 2: Path Selection Tree (Conditional) -->
          <div v-show="showPathTree" class="path-tree-section">
            <h3 class="section-title">选择新路径</h3>
            <div class="path-tree-wrapper">
              <el-tree
                v-loading="loadingTree"
                ref="pathTreeRef"
                :data="pathTreeData"
                node-key="id"
                show-checkbox
                check-strictly
                :props="treeProps"
                @check="handleCheckChange"
                class="storage-path-tree"
                highlight-current
                :expand-on-click-node="false"
              >
                <template #default="{ node, data }">
                  <span class="custom-tree-node">
                    <!-- SVG Folder Icon -->
                    <svg
                      v-if="data.directory"
                      class="folder-icon"
                      viewBox="0 0 1024 1024"
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                    >
                      <path
                        d="M928 444H820V235.2c0-4.4-3.6-8-8-8h-230.4c-11.2 0-22.1-2.4-32-7.2l-48-24c-4.5-2.2-9.5-3.6-14.7-3.6H256c-4.4 0-8 3.6-8 8v100H96c-17.7 0-32 14.3-32 32v448c0 17.7 14.3 32 32 32h832c17.7 0 32-14.3 32-32V476c0-17.7-14.3-32-32-32zM256 192h210.1c1.3 0 2.5.3 3.7.8l48 24c13.8 6.9 29.1 10.4 44.4 10.4H812v176H208V235.2c0-19.8 16.1-36 36-36h12zm700 608H96V412h832v388z"
                        fill="currentColor"
                      />
                    </svg>
                    <!-- Placeholder for file icon if needed -->
                    <!-- <svg v-else class="file-icon">...</svg> -->
                    <span :title="node.label">{{ node.label }}</span>
                  </span>
                </template>
              </el-tree>
            </div>
            <div class="tree-actions">
              <el-button @click="showPathTree = false">取消</el-button>
              <el-button
                type="primary"
                @click="saveNewPath"
                :loading="isLoading"
                >确定</el-button
              >
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { Check, Delete, EditPen, RefreshLeft } from "@element-plus/icons-vue";
import apiAjax from "@/api/index";

// --- 常量 ---
const API_ENDPOINTS = {
  GET_STORAGE_PATH: "/api/jnx/fusion/apps/satellite/storagePath/get",
  MANAGE_STORAGE_PATH: "/api/jnx/fusion/apps/satellite/storagePath/manage",
  GET_WORK_FOLDER: "/api/jnx/fusion/apps/satellite/storagePath/workFolder/get",
};

const STATUS_CODES = {
  ACTIVE: 1,
  HISTORY: 0,
  DEACTIVATED: -1,
};

// --- 响应式状态 ---
const showPathTree = ref(false);
const selectedPath = ref("");
const pathTreeRef = ref(null);
const currentStoragePath = ref("");
const pathHistoryList = ref([]);
const pathTreeData = reactive([]);
const isLoading = ref(false);
const loadingTree = ref(false);

// --- Tree Props ---
const treeProps = {
  label: "label",
  children: "children",
  isLeaf: "isLeaf",
};

// --- 计算属性 ---
const hasActivePath = computed(() =>
  pathHistoryList.value.some((item) => item.status === STATUS_CODES.ACTIVE),
);

// --- API 调用封装 ---
const callManageApi = async (payload, successMessage, errorMessagePrefix) => {
  isLoading.value = true;
  try {
    const minimalPayload = { ...payload };
    await apiAjax.post(API_ENDPOINTS.MANAGE_STORAGE_PATH, minimalPayload, {
      info: true,
    });
    await fetchStoragePathData();
    return true;
  } catch (error) {
    console.log(err);
    return false;
  } finally {
    isLoading.value = false;
  }
};

// --- 数据获取 ---
const fetchStoragePathData = async () => {
  isLoading.value = true;
  try {
    const { data } = await apiAjax.get(API_ENDPOINTS.GET_STORAGE_PATH);
    if (data) {
      currentStoragePath.value = data.localStorageRootPath || "";
      pathHistoryList.value = data.storageRecords || [];
    } else {
      ElMessage.error("获取存储路径数据失败: 响应数据为空");
      currentStoragePath.value = "";
      pathHistoryList.value = [];
    }
  } catch (error) {
    console.error("获取存储路径数据出错:", error);
    const errorMsg =
      error?.response?.data?.message || error?.message || "未知错误";
    ElMessage.error(`获取存储路径数据出错: ${errorMsg}`);
    currentStoragePath.value = "";
    pathHistoryList.value = [];
  } finally {
    isLoading.value = false;
  }
};

// --- 辅助：转换 API 数据为 Tree 数据 ---
const transformDataForTree = (nodes) => {
  return nodes.map((node) => {
    let label = node.name;
    // 为根驱动器节点添加磁盘空间信息
    if (
      node.fullPath &&
      node.fullPath.match(/^[A-Za-z]:\\?$/) && // 匹配如 C:\ 或 D:\
      node.totalSpace !== null && // 确保有空间信息
      node.freeSpace !== null
    ) {
      // 保留一位小数
      const totalGB = parseFloat(node.totalSpace).toFixed(1);
      const freeGB = parseFloat(node.freeSpace).toFixed(1);
      label = `${node.fullPath} (可用: ${freeGB} GB / 总共: ${totalGB} GB)`;
    } else {
      label = node.name; // 文件夹只显示名称
    }

    const transformedNode = {
      id: node.fullPath, // 使用 fullPath 作为唯一 ID
      label: label,
      fullPath: node.fullPath, // 保留原始 fullPath
      directory: node.directory,
      children: node.children ? transformDataForTree(node.children) : [],
    };
    // el-tree 可以根据 children 是否为空数组判断是否为叶子节点，但显式添加 isLeaf 可能更清晰
    transformedNode.isLeaf =
      !transformedNode.children || transformedNode.children.length === 0;
    return transformedNode;
  });
};

// --- 获取工作目录并处理 ---
const fetchWorkFolder = async () => {
  if (loadingTree.value) return; // 防止重复加载
  loadingTree.value = true;
  pathTreeData.splice(0, pathTreeData.length); // 清空现有数据
  try {
    const { data } = await apiAjax.get(API_ENDPOINTS.GET_WORK_FOLDER); // 实际 API 调用
    if (data && Array.isArray(data)) {
      const transformed = transformDataForTree(data);
      // 使用 splice 更新 reactive 数组以保持响应性
      pathTreeData.splice(0, pathTreeData.length, ...transformed);

      // 数据加载完成后，在下一个 tick 中尝试预选当前路径
      await nextTick();
      preselectCurrentPath();
    } else {
      ElMessage.error("获取工作目录失败: 响应数据格式不正确或为空");
      pathTreeData.splice(0, pathTreeData.length); // 出错时也清空
    }
  } catch (error) {
    console.error("获取工作目录出错:", error);
    const errorMsg =
      error?.response?.data?.message || error?.message || "未知错误";
    ElMessage.error(`获取工作目录出错: ${errorMsg}`);
    pathTreeData.splice(0, pathTreeData.length); // 出错时也清空
  } finally {
    loadingTree.value = false;
  }
};

// --- 辅助：在树中预选当前路径并展开 ---
const preselectCurrentPath = () => {
  if (
    currentStoragePath.value &&
    pathTreeRef.value &&
    pathTreeData.length > 0
  ) {
    const node = pathTreeRef.value.getNode(currentStoragePath.value);

    if (node) {
      // 找到了节点，设置勾选状态
      pathTreeRef.value.setCheckedKeys([currentStoragePath.value], false);
      selectedPath.value = currentStoragePath.value; // 更新当前选择的路径

      // --- 新增：展开父节点 ---
      let parent = node.parent;
      while (parent && parent.key !== undefined) {
        // 循环直到根节点（根节点通常没有 key 或 key 是 null/undefined）
        parent.expand(); // 展开父节点
        parent = parent.parent; // 继续向上查找
      }
      // --- 结束新增 ---

      // （可选）尝试将节点设为当前高亮节点，这可能有助于定位，但不是必须的
      // pathTreeRef.value.setCurrentKey(currentStoragePath.value);

      // （可选）将节点滚动到视图中 - 这比较复杂，可能需要 DOM 操作
      // nextTick(() => {
      //     const nodeElement = document.querySelector(`.el-tree-node[data-key="${currentStoragePath.value}"]`);
      //     if (nodeElement) {
      //         nodeElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      //     }
      // });
    } else {
      // 当前存储路径在获取的目录树中未找到
      console.warn(`当前路径 ${currentStoragePath.value} 在目录树中未找到。`);
      pathTreeRef.value.setCheckedKeys([], false); // 清空选择
      selectedPath.value = "";
    }
  } else if (pathTreeRef.value) {
    // 没有当前路径或树组件无效，清空选择
    pathTreeRef.value.setCheckedKeys([], false);
    selectedPath.value = "";
  }
};

// --- 事件处理 ---
const togglePathTreeVisibility = async () => {
  const becomingVisible = !showPathTree.value;
  showPathTree.value = !showPathTree.value;

  if (becomingVisible) {
    // 只有在首次打开或数据为空时才加载
    if (pathTreeData.length === 0) {
      await fetchWorkFolder(); // fetchWorkFolder 内部会处理预选
    } else {
      // 如果数据已存在，确保在 nextTick 后重新应用预选状态
      await nextTick();
      preselectCurrentPath();
    }
  } else {
    // 关闭时可以考虑清空选择状态，如果需要的话
    // selectedPath.value = "";
    // if (pathTreeRef.value) {
    //   pathTreeRef.value.setCheckedKeys([], false);
    // }
  }
};

const handleCheckChange = (nodeData, checkedInfo) => {
  const checkedKeys = checkedInfo.checkedKeys;
  // 强制单选逻辑
  if (checkedKeys.length > 0) {
    const currentKey = nodeData.id; // id 就是 fullPath
    if (pathTreeRef.value) {
      pathTreeRef.value.setCheckedKeys([currentKey], false);
    }
    selectedPath.value = nodeData.fullPath; // 从转换后的数据中获取 fullPath
  } else {
    // 用户取消勾选
    selectedPath.value = "";
    if (pathTreeRef.value) {
      pathTreeRef.value.setCheckedKeys([], false); // 确保没有节点被勾选
    }
  }
};

const saveNewPath = async () => {
  if (!selectedPath.value) {
    ElMessage.warning("请选择一个存储路径");
    return;
  }

  // 检查选择的路径是否与当前路径相同
  if (selectedPath.value === currentStoragePath.value) {
    ElMessage.info("选择的路径与当前路径相同，无需更改。");
    showPathTree.value = false; // 直接关闭选择区域
    return;
  }
  const payload = {
    fullPath: selectedPath.value,
    status: STATUS_CODES.ACTIVE, // 新增路径总是设为激活状态
  };
  const success = await callManageApi(
    payload,
    "存储路径新增并设为当前成功",
    "新增存储路径",
  );
  if (success) {
    showPathTree.value = false; // 关闭选择区域
    // 清空树的选择状态，以备下次打开
    selectedPath.value = "";
    if (pathTreeRef.value) {
      pathTreeRef.value.setCheckedKeys([], false);
    }
  }
};

const handleSetCurrent = (row) => {
  if (row.status !== STATUS_CODES.HISTORY) {
    ElMessage.warning("只有历史路径才能设为当前路径。");
    return;
  }

  ElMessageBox.confirm(
    `确定要将路径 "${row.fullPath}" 设为当前存储路径吗？原当前路径将变为历史路径。`,
    "提示",
    { confirmButtonText: "确定", cancelButtonText: "取消", type: "warning" },
  )
    .then(async () => {
      const payload = {
        id: row.id,
        fullPath: row.fullPath,
        status: STATUS_CODES.ACTIVE,
      };
      await callManageApi(payload, "设置成功", "设置当前路径");
    })
    .catch(() => {
      ElMessage.info("操作已取消");
    });
};

const handleDeactivate = (row) => {
  if (row.status === STATUS_CODES.DEACTIVATED) {
    ElMessage.warning("该路径已作废");
    return;
  }

  const confirmMessage = `确定要作废路径 "${row.fullPath}" 吗？${row.status === STATUS_CODES.ACTIVE ? " 当前路径作废后可能无可用存储路径，请谨慎操作！" : ""} `;

  ElMessageBox.confirm(confirmMessage, "警告", {
    confirmButtonText: "确定作废",
    cancelButtonText: "取消",
    type: "error",
  })
    .then(async () => {
      const payload = {
        id: row.id,
        fullPath: row.fullPath,
        status: STATUS_CODES.DEACTIVATED,
      };
      await callManageApi(payload, "作废成功", "作废路径");
    })
    .catch(() => {
      ElMessage.info("操作已取消");
    });
};

/**
 * 恢复一个已作废的存储路径（将其状态设置为 0 - 历史路径）。
 * @param {object} row - 被操作的表格行数据。
 */
const handleRestore = (row) => {
  // 校验：只有已作废的路径才能被恢复
  if (row.status !== STATUS_CODES.DEACTIVATED) {
    ElMessage.warning("只有已作废的路径才能恢复。");
    return;
  }

  ElMessageBox.confirm(
    `确定要恢复路径 "${row.fullPath}" 吗？恢复后状态将变为"历史路径"。`,
    "提示",
    {
      confirmButtonText: "确定恢复",
      cancelButtonText: "取消",
      type: "warning",
    },
  )
    .then(async () => {
      const payload = {
        id: row.id, // 需要提供记录 ID
        fullPath: row.fullPath, // 如果后端需要则包含
        status: STATUS_CODES.HISTORY, // 目标状态为"历史路径"
      };
      await callManageApi(payload, "恢复成功", "恢复路径");
    })
    .catch(() => {
      // 用户点击了"取消"
      ElMessage.info("操作已取消");
    });
};

// --- 辅助函数 ---
const getStatusText = (status) => {
  switch (status) {
    case STATUS_CODES.ACTIVE:
      return "当前在用";
    case STATUS_CODES.HISTORY:
      return "历史路径";
    case STATUS_CODES.DEACTIVATED:
      return "已作废";
    default:
      return "未知";
  }
};

const getStatusTagType = (status) => {
  switch (status) {
    case STATUS_CODES.ACTIVE:
      return "success";
    case STATUS_CODES.HISTORY:
      return "info";
    case STATUS_CODES.DEACTIVATED:
      return "danger";
    default:
      return "warning";
  }
};

// --- 生命周期钩子 ---
onMounted(() => {
  fetchStoragePathData(); // 获取历史记录和当前路径
  // 不再默认加载工作目录树
  // fetchWorkFolder();
});
</script>

<style lang="scss" scoped>
.con {
  height: calc(100vh - 50px);
  display: flex;
  flex-direction: column;
  padding: 16px;
  box-sizing: border-box;
  background-color: #f4f7fa;
}

.card-con {
  width: 100%;
  flex: 1;
  display: flex;
}

.cardBox {
  width: 100%;
  border: 1px solid #dcdfe6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  background-color: #fff;

  :deep(.el-card__header) {
    padding: 16px 20px;
    border-bottom: 1px solid #e4e7ed;
    background-color: #fbfcfd;
  }

  :deep(.el-card__body) {
    padding: 0;
    flex: 1;
    overflow: hidden;
    display: flex;
  }
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;

  .header-title {
    font-weight: 600;
    font-size: 17px;
    color: #303133;
    display: flex;
    align-items: baseline;
  }

  .current-path-display {
    font-size: 14px;
    color: #5a5e66;
    margin-left: 12px;
    font-weight: normal;
  }
}

.main-content-area {
  display: flex;
  flex: 1;
  height: 100%;
  overflow: hidden;
}

.path-history-container {
  flex: 1;
  padding: 15px 20px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.path-tree-section {
  width: 400px;
  flex-shrink: 0;
  padding: 15px 20px;
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  overflow: hidden;
  transition: width 0.3s ease;
  border-left: 1px solid #e4e7ed;
  box-sizing: border-box;
}

.section-title {
  font-size: 15px;
  font-weight: 600;
  color: #495057;
  margin: 0 0 12px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e9ecef;
  flex-shrink: 0;
}

.path-history {
  flex: 1;
  overflow: hidden;
  width: 100%;
}

.path-table {
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;

  height: calc(100% - 0px);

  :deep(.el-table__cell) {
    padding: 10px 0;
    font-size: 14px;
    color: #606266;
    font-weight: normal;
  }

  :deep(td.el-table__cell) {
    vertical-align: middle;
    .el-button {
      margin: 0 3px;
    }
  }

  :deep(.el-tag) {
    border-radius: 4px;
  }
}

.path-tree-wrapper {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 15px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background-color: #fff;
  padding: 10px;
  box-sizing: border-box;
  position: relative;
}

.storage-path-tree {
  :deep(.el-tree-node__content) {
    height: 32px;
    line-height: 32px;
    &:hover {
      background-color: #f5f7fa;
    }
  }
  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #e4f0ff;
    color: #409eff;
    .folder-icon {
      color: #409eff;
    }
  }

  .custom-tree-node {
    display: flex;
    align-items: center;
    font-size: 14px;
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding-right: 5px;

    .folder-icon {
      margin-right: 6px;
      color: #e6a23c;
      vertical-align: middle;
      flex-shrink: 0;
    }

    .file-icon {
      margin-right: 6px;
      color: #909399;
      vertical-align: middle;
      flex-shrink: 0;
    }

    span {
      vertical-align: middle;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  :deep(.el-checkbox) {
    margin-right: 8px;
  }
}

.tree-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 15px;
  border-top: 1px solid #e9ecef;
  flex-shrink: 0;

  .el-button {
    margin-left: 10px;
  }
}

:deep(.el-button) {
  border-radius: 4px;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}
::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}
::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}
::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
