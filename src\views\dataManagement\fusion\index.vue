<template>
  <div class="fusion-container">
    <navList
      :navList="realTimeData.navList"
      :index="realTimeData.navIindex"
      @setoriData="setoriData"
    />
    <div class="fusion-content">
      <groundSatellite v-if="realTimeData.navIindex === 0" />
      <satellite v-if="realTimeData.navIindex === 1" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive } from "vue";
import { Search } from "@element-plus/icons-vue";

import navList from "@/components/navList/navList.vue";
import groundSatellite from "./groundSatellite.vue";
import satellite from "./satellite.vue";
const realTimeData = reactive({
  navList: [
    {
      id: 0,
      name: "星地融合授时信息",
    },
    { id: 1, name: "卫星伪码授时增强信息" },
  ],
  navIindex: 0,
});
const setoriData = (index) => {
  realTimeData.navIindex = index;
};
</script>

<style scoped lang="scss">
.fusion-container {
  display: flex;
  margin-top: 16px;
  flex-direction: column;
  height: 91vh;
  .fusion-content {
    margin: 16px 0px;
    // padding: 16px;
    border-radius: 6px;
    flex: 1;
    background-color: #fff;
    :deep(.el-card__body) {
      padding: 16px;
      padding-bottom: 6px;
    }
  }
}
</style>
