<script setup lang="ts">
import { computed, reactive } from "vue";
import { $t } from "@/locales";
import { useRouterPush } from "@/hooks/common/router";
import { useFormRules, useNaiveForm } from "@/hooks/common/form";
import { useAuthStore } from "@/store/modules/auth";

defineOptions({
  name: "PwdLogin",
});

const authStore = useAuthStore();
const { formRef, validate } = useNaiveForm();

interface FormModel {
  userName: string;
  password: string;
}

const model: FormModel = reactive({
  userName: "admin",
  password: "admin",
});

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  // inside computed to make locale reactive, if not apply i18n, you can define it without computed
  const { formRules } = useFormRules();

  return {
    userName: formRules.userName,
    password: formRules.pwd,
  };
});

async function handleSubmit() {
  console.log("登录");
  await validate();
  // const data = await apiAjax.get("/hello");
  await authStore.login(model.userName, model.password);
}

// type AccountKey = "super" | "admin" | "user";

// interface Account {
//   key: AccountKey;
//   label: string;
//   userName: string;
//   password: string;
// }

// const accounts = computed<Account[]>(() => [
//   {
//     key: "super",
//     label: $t("page.login.pwdLogin.superAdmin"),
//     userName: "Super",
//     password: "123456",
//   },
//   {
//     key: "admin",
//     label: $t("page.login.pwdLogin.admin"),
//     userName: "Admin",
//     password: "123456",
//   },
//   {
//     key: "user",
//     label: $t("page.login.pwdLogin.user"),
//     userName: "User",
//     password: "123456",
//   },
// ]);

// async function handleAccountLogin(account: Account) {
//   await authStore.login(account.userName, account.password);
// }
</script>

<template>
  <div class="con">
  <NForm
    ref="formRef"
    :model="model"
    :rules="rules"
    size="large"
      label-placement="left"
      :show-require-mark="false"
      :label-width="70"
  >
      <NFormItem path="userName" label="用户名：">
        <NInput v-model:value="model.userName" placeholder="请输入" />
    </NFormItem>
      <NFormItem path="password" label="密码：">
      <NInput
        v-model:value="model.password"
        type="password"
        show-password-on="click"
          placeholder="请输入"
      />
    </NFormItem>
    </NForm>

    <div class="bottom-com">
      <NButton
        color="#34A9FF"
        class="login-btn"
        round
        block
        :loading="authStore.loginLoading"
        @click="handleSubmit"
      >
        登录
      </NButton>
      </div>
      </div>
</template>

<style lang="scss" scoped>
.con {
  width: 100%;

  :deep(.n-form-item-label__text) {
    font-weight: 400;
    font-size: 14px;
    color: rgba(0,0,0,0.65);
    line-height: 22px;
    text-align: left;
    font-style: normal;
  }
}
.bottom-com {
  margin-top: 14px;
  display: flex;
  flex-direction: row;
  justify-content: center;

  .login-btn {
    width: 300px;
    height: 52px;
    border-radius: 2px;

    :deep(.n-button__content) {
      font-weight: 400;
      font-size: 14px;
      color: #FFFFFF;
      line-height: 22px;
      text-align: left;
      font-style: normal;
    }
  }
}
</style>
