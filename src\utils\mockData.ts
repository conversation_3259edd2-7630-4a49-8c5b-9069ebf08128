// 日期快捷选项
export const dateShortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    },
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    },
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
      return [start, end]
    },
  },
];

// 禁用未来日期
export const disabledDate = (time: Date) => {
  return time.getTime() > Date.now()
};

// 生成模拟数据
export const generateMockData = (count: number, type: string) => {
  const result = [];
  const frequencyIds = ["B1I", "B1C", "B2a", "B2b", "B3I", "L1", "L2", "L5", "E1", "E5a", "E5b", "E6"];
  const utcGnssTypes = ["UTC-BDS (ns)", "UTC-GPS (ns)", "UTC-GLONASS (ns)", "UTC-GALILEO (ns)"];
  const satelliteIds = Array.from({length: 63}, (_, i) => `C${String(i + 1).padStart(2, '0')}`);

  for (let i = 0; i < count; i++) {
    const date = new Date();
    date.setHours(date.getHours() - i);
    const dateStr = date.toISOString().replace("T", " ").substring(0, 19);
    const satelliteIndex = i % satelliteIds.length;
    const utcGnssTypeIndex = i % utcGnssTypes.length;
    const frequencyIdIndex = i % frequencyIds.length;

    if (type === "groundLeft") {
      result.push({
        utcTime: dateStr,
        utcGnssType: utcGnssTypes[utcGnssTypeIndex],
        frequencyId: frequencyIds[frequencyIdIndex],
        timingBias: (Math.random() * 0.1).toFixed(5),
      });
    } else if (type === "groundRight") {
      result.push({
        paramGenerateTime: dateStr,
        utcGnssType: utcGnssTypes[utcGnssTypeIndex],
        frequencyId: frequencyIds[frequencyIdIndex],
        toc: Math.floor(Math.random() * 1000000).toString(),
        a0: (Math.random() * 10).toFixed(3),
        a1: (Math.random() * 10).toFixed(3),
        a2: (Math.random() * 10).toFixed(3),
      });
    } else if (type === "satelliteLeft") {
      result.push({
        utcTime: dateStr,
        satelliteId: satelliteIds[satelliteIndex],
        frequencyId: frequencyIds[frequencyIdIndex],
        timeDiffBias: (Math.random() * 0.1).toFixed(5),
      });
    } else if (type === "satelliteRight") {
      result.push({
        paramGenerateTime: dateStr,
        satelliteNumber: satelliteIds[satelliteIndex],
        frequencyId: frequencyIds[frequencyIdIndex],
        toc: Math.floor(Math.random() * 1000000).toString(),
        a0: (Math.random() * 10).toFixed(3),
        a1: (Math.random() * 10).toFixed(3),
        a2: (Math.random() * 10).toFixed(3),
      });
    }
  }
  return result;
};
