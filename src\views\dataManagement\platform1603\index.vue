<template>
  <div class="container-wrapper">
    <el-card class="main-card">
      <div class="search-area">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="导航系统">
            <el-select
              style="width: 180px"
              v-model="searchForm.systemCode"
              placeholder="请选择系统"
              @change="handleSystemChange"
            >
              <el-option label="全部" value="" />
              <el-option
                v-for="item in systemOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="频点标识">
            <el-select
              style="width: 180px"
              v-model="searchForm.frequencyId"
              placeholder="请选择频点标识"
            >
              <el-option label="全部" value="" />
              <el-option
                v-for="item in prnOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="时间">
            <el-date-picker
              style="width: 300px"
              v-model="searchForm.timeRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              :disabled-date="configure.disabledDate"
              :shortcuts="configure.dateShortcuts"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch">查询</el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="tables-container">
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <span>单星授时偏差列表</span>
            </div>
          </template>
          <el-table
            :data="singleSatelliteData"
            border
            stripe
            style="width: 100%"
            class="tabs-table"
            v-loading="loading"
            :header-cell-style="headerCellStyle"
            :cell-style="cellStyle"
          >
            <el-table-column prop="timestamp" width="180" label="数据产生时间" />
            <el-table-column prop="navigationSystemCode" width="180" label="时差类型">
              <template #default="scope">
                {{ getSystemName(scope.row.navigationSystemCode) }}
              </template>
            </el-table-column>
            <el-table-column prop="satellitePrn" label="PRN" />
            <el-table-column prop="frequencyId" label="频点标识" />
            <el-table-column prop="timingDeviation" label="授时偏差(ns)" >
              <template #default="scope">
                {{ scope.row.timingDeviation?.toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container">
            <el-pagination
              v-model:current-page="singleSatelliteCurrentPage"
              v-model:page-size="singleSatellitePageSize"
              :page-sizes="[10, 20, 30, 50]"
              layout="sizes, total, prev, pager, next, jumper"
              :total="singleSatelliteTotal"
              @current-change="handleSingleSatelliteCurrentChange"
              @size-change="handleSingleSatelliteSizeChange"
            />
          </div>
        </el-card>
        <el-card class="table-card">
          <template #header>
            <div class="card-header">
              <span>多星授时偏差列表</span>
            </div>
          </template>
          <el-table
            :data="multiSatelliteData"
            border
            stripe
            style="width: 100%"
            class="tabs-table"
            v-loading="loading"
            :header-cell-style="headerCellStyle"
            :cell-style="cellStyle"
          >
            <el-table-column prop="timestamp" width="180" label="数据产生时间" />
            <el-table-column prop="navigationSystemCode" width="180" label="时差类型">
              <template #default="scope">
                {{ getSystemName(scope.row.navigationSystemCode) }}
              </template>
            </el-table-column>


            <el-table-column prop="frequencyId" label="频点标识" />
            <el-table-column prop="timingDeviation" label="授时偏差(ns)" >
              <template #default="scope">
                {{ scope.row.timingDeviation?.toFixed(2) }}
              </template>
            </el-table-column>
          </el-table>

          <div class="pagination-container">
            <el-pagination
              v-model:current-page="multiSatelliteCurrentPage"
              v-model:page-size="multiSatellitePageSize"
              :page-sizes="[10, 20, 30, 50]"
              layout="sizes, total, prev, pager, next, jumper"
              :total="multiSatelliteTotal"
              @current-change="handleMultiSatelliteCurrentChange"
              @size-change="handleMultiSatelliteSizeChange"
            />
          </div>
        </el-card>


      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive } from "vue";
import configure from "@/utils/configure.js";
import apiAjax from "@/api/index";
import { Search } from "@element-plus/icons-vue";
import { useAllSatellite } from "@/store/modules/allSatellite";
const allSatelliteStore = useAllSatellite();

const loading = ref(false);

// 表格数据和分页
const multiSatelliteData = ref([]);
const singleSatelliteData = ref([]);
const multiSatelliteTotal = ref(0);
const singleSatelliteTotal = ref(0);

const searchForm = reactive({
  systemCode: "",
  prn: "",
  timeRange: [],
});

// 导航系统选项
const systemOptions = computed(() => {
  if (!allSatelliteStore.data || !allSatelliteStore.data.length) return [];
  return allSatelliteStore.data.map((system) => ({
    value: system.systemCode,
    label: system.systemName,
  }));
});

// 卫星所有频点
const prnOptions = computed(() => {
  if (!searchForm.systemCode || !allSatelliteStore.data) return [];

  // 找到选中的导航系统
  const selectedSystem = allSatelliteStore.data.find(
    (system) => system.systemCode === searchForm.systemCode,
  );

  if (!selectedSystem || !selectedSystem.frequencyCombinations) return [];

  // 返回该系统下的所有频点
  return selectedSystem.frequencyCombinations.map((satellite) => ({
    value: satellite.frequencyId,
    label: satellite.frequencyId,
  }));
});

// 处理导航系统变更
const handleSystemChange = () => {
  // 清空PRN选择
  searchForm.frequencyId = "";
};

// 表头样式
const headerCellStyle = {
  background: "#f5f7fa",
  color: "#606266",
  fontWeight: "bold",
  fontSize: "18px",
  height: "50px",
};

// 单元格样式
const cellStyle = {
  fontSize: "16px",
  padding: "10px 0",
};

// 分页配置 - 修改为每个表格独立的响应式变量
const multiSatellitePageSize = ref(10);
const singleSatellitePageSize = ref(10);
const multiSatelliteCurrentPage = ref(1);
const singleSatelliteCurrentPage = ref(1);

// 分页处理函数
const handleMultiSatelliteCurrentChange = (val) => {
  multiSatelliteCurrentPage.value = val;
  loadMultiSatelliteData();
};

const handleSingleSatelliteCurrentChange = (val) => {
  singleSatelliteCurrentPage.value = val;
  loadSingleSatelliteData();
};

// 添加分页大小变化处理函数
const handleMultiSatelliteSizeChange = (val) => {
  multiSatellitePageSize.value = val;
  // 当页面大小变化时，重置页为第一页
  multiSatelliteCurrentPage.value = 1;
  // 重新加载数据
  loadMultiSatelliteData();
};

const handleSingleSatelliteSizeChange = (val) => {
  singleSatellitePageSize.value = val;
  // 当页面大小变化时，重置页为第一页
  singleSatelliteCurrentPage.value = 1;
  // 重新加载数据
  loadSingleSatelliteData();
};

// 构建查询参数
const buildQueryParams = (page, size) => {
  const params = new URLSearchParams();

  // 导航系统编码
  if (searchForm.systemCode) {
    params.append("navigationSystemCode", searchForm.systemCode);
  }

  // 卫星PRN码
  if (searchForm.frequencyId) {
    params.append("frequencyId", searchForm.frequencyId);
  }

  // 时间范围参数
  if (searchForm.timeRange?.length === 2) {
    params.append(
      "beginTime",
      configure.formatDate(new Date(`${searchForm.timeRange[0]} 00:00:00`)),
    );
    params.append(
      "endTime",
      configure.formatDate(new Date(`${searchForm.timeRange[1]} 23:59:59`)),
    );
  }

  // 分页参数
  params.append("page", page); // 后端分页从1开始
  params.append("size", size);

  return params.toString();
};

// 加载多星授时偏差数据
const loadMultiSatelliteData = async () => {
  loading.value = true;
  try {
    const queryString = buildQueryParams(
      multiSatelliteCurrentPage.value,
      multiSatellitePageSize.value,
    );
    const { data: response } = await apiAjax.get(
      `/api/jnx/fusion/apps/satellite/1603TimingDeviation/findMultiSatelliteBiasPage?${queryString}`,
    );

    if (response) {
      multiSatelliteData.value = response.content || [];
      multiSatelliteTotal.value = response.totalElements || 0;
    }
  } catch (error) {
    console.error("获取多星授时偏差数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 加载单星授时偏差数据
const loadSingleSatelliteData = async () => {
  loading.value = true;
  try {
    const queryString = buildQueryParams(
      singleSatelliteCurrentPage.value,
      singleSatellitePageSize.value,
    );
    const { data: response } = await apiAjax.get(
      `/api/jnx/fusion/apps/satellite/1603TimingDeviation/findSingleSatelliteBiaPage?${queryString}`,
    );

    if (response) {
      singleSatelliteData.value = response.content || [];
      singleSatelliteTotal.value = response.totalElements || 0;
    }
  } catch (error) {
    console.error("获取单星授时偏差数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理函数
const onSearch = () => {
  console.log("搜索条件：", searchForm);
  multiSatelliteCurrentPage.value = 1;
  singleSatelliteCurrentPage.value = 1;

  // 加载数据
  loadMultiSatelliteData();
  loadSingleSatelliteData();
};

// 添加导航系统编码翻译函数
const getSystemName = (code) => {
  if (!code) return '';

  // 找到对应的系统
  const system = allSatelliteStore.data?.find(sys => sys.systemCode === code);
  return system ? system.systemName : code; // 如果找不到对应的名称，则显示原始编码
};

// 模拟加载数据
onMounted(async () => {
  await allSatelliteStore.initAllData();
  console.log(allSatelliteStore.data);

  // 初始加载数据
  loadMultiSatelliteData();
  loadSingleSatelliteData();
});
</script>

<style scoped lang="scss">
.container-wrapper {
  width: 100%;
  background-color: #f5f7fa;

  .page-header {
    // margin-bottom: 20px;
    h3 {
      font-size: 1.5rem;
      color: #303133;
      margin: 0;
      padding-bottom: 10px;
      border-bottom: 1px solid #ebeef5;
    }
  }

  .main-card {
    // margin-bottom: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }

  .filter-container {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 16px;

    .filter-item {
      &.date-picker-container {
        flex: 1;
        min-width: 300px;
      }

      &.button-container {
        flex: 0 0 auto;
      }
    }
  }

  .fusion-tabs {
    margin-top: 10px;
  }

  .tables-container {
    display: flex;
    gap: 20px;
    margin-top: 10px;
    .table-card {
      flex: 1;
      min-width: 0;
      margin-bottom: 20px;

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        span {
          font-size: 16px;
          font-weight: bold;
        }
      }
    }
    .table-card2 {
      flex: 1.5;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}

:deep(.el-table) {
  &.el-table--striped .el-table__body tr.el-table__row--striped td {
    background-color: #fafafa;
  }

  .el-table__header th {
    background-color: #f5f7fa;
    color: #606266;
    font-weight: bold;
  }
}
.tabs-table {
  height: 53vh;
}
@media (max-height: 1080px) {
  .tabs-table {
    height: 60vh;
  }
}
</style>
