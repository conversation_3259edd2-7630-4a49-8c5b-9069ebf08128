/**
 * Created by zcr on 2018/1/25.
 */

let proxySource = {
  "/Api": "http://192.168.111.15:8080", // 外网测试环境
  // htpp
},
  proxyRoot = [];
for (let key in proxySource) {
  proxyRoot.push(
    process.env.NODE_ENV === "production" ? proxySource[key] : key + "/"
  );
}
export default {
  prefix: "temp_", //项目前缀
  xAppVersion: "1.0.0", //版本
  base_path: "", //  项目地址
  proxySource: proxySource, //代理源配置
  root: proxyRoot, // 配置API接口地址
  proxyUrl: {
    defult: "",
  },
  publicKey: "LTAI4GKXdB0hcaFS78629b526a153b17", //app 加密密钥
  publicKeyRecharge: "jstxbetcUroad123", //圈存加密密钥
  publicKeyMd5Recharge: "LTAI4GKXdB0hcaFS78629b526a153b17",
  isTest: false, //是否测试环境
}

// module.exports = {
//   ...proxy
// };
