<template>
  <div class="b2b-ppp-diff">
    <div class="chart-content">
      <line-chart :data="lineChartOption" />
    </div>
    <div class="table-wrapper">
      <!-- <div class="table-title">星地融合授时偏差</div> -->
      <el-table
        :data="timeDiffList"
        border
        style="width: 100%"
        height="310"
      >
      <el-table-column prop="time" label="UTC-时间" width="180" :formatter="(row) => formatTime(row.time)" />
        <el-table-column prop="UTC(NTSC)-BDT" label="UTC(NTSC)-BDT (ns)" />
        <el-table-column prop="UTC(NTSC)-GPST" label="UTC(NTSC)-GPST (ns)" />
        <el-table-column prop="UTC(NTSC)-GLNT" label="UTC(NTSC)-GLNT (ns)" />
        <el-table-column prop="UTC(NTSC)-GST" label="UTC(NTSC)-GST (ns)" />
      </el-table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import LineChart from "@/components/lineChart/lineChart.vue";

// 完整时间格式化函数
const formatTime = (timeStr) => {
  const date = new Date(timeStr);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};
// 修改时间格式化函数
const formatUTCTime = (date) => {
  return date.toISOString().replace('T', ' ').slice(0, -5);
};

// 然后再定义其他变量和函数
const lineChartOption = ref({
  xAxis: {
    type: 'category',
    data: Array.from({ length: 24 }, (_, i) => `${String(i).padStart(2, '0')}:00`),
  },
  yAxis: {
    type: 'value',
    name: '时差(ns)',
  },
  legend: {
    bottom: 10,
    data: ['UTC(NTSC)-BDT', 'UTC(NTSC)-GPST', 'UTC(NTSC)-GLNT', 'UTC(NTSC)-GST'],
  },
  grid: {
    top: '30px',
    left: '50px',
    right: '30px',
    bottom: '60px',
  },
  tooltip: {
    trigger: 'axis',
    formatter: function(params) {
      // 获取当前日期
      const now = new Date();
      // 提取小时数
      const hour = parseInt(params[0].name.split(':')[0]);
      // 创建完整的UTC时间
      const date = new Date(now.getFullYear(), now.getMonth(), now.getDate(), hour, 0, 0);
      const fullTime = formatUTCTime(date);

      let result = `UTC时间: ${fullTime}<br/>`;
      params.forEach(param => {
        result += `${param.seriesName}: ${param.value} ns<br/>`;
      });
      return result;
    }
  },
  series: [
    {
      name: 'UTC(NTSC)-BDT',
      data: Array.from({ length: 24 }, () => (Math.random() * 200 - 100).toFixed(1)),
      type: 'line',
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: { color: '#3B8CFF' },
      lineStyle: { width: 2 },
    },
    {
      name: 'UTC(NTSC)-GPST',
      data: Array.from({ length: 24 }, () => (Math.random() * 200 - 100).toFixed(1)),
      type: 'line',
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: { color: '#FFA07A' },
      lineStyle: { width: 2 },
    },
    {
      name: 'UTC(NTSC)-GLNT',
      data: Array.from({ length: 24 }, () => (Math.random() * 200 - 100).toFixed(1)),
      type: 'line',
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: { color: '#4CAF50' },
      lineStyle: { width: 2 },
    },
    {
      name: 'UTC(NTSC)-GST',
      data: Array.from({ length: 24 }, () => (Math.random() * 200 - 100).toFixed(1)),
      type: 'line',
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: { color: '#FF6347' },
      lineStyle: { width: 2 },
    },
  ],
});

// 生成星地融合授时偏差数据
const timeDiffList = ref([]);
const generateTimeDiffList = () => {
  const now = new Date();
  timeDiffList.value = Array.from({ length: 10 }, (_, i) => {
    const time = new Date(now - i * 60000);
    return {
      time: time.toISOString(),
      "UTC(NTSC)-BDT": (Math.random() * 200 - 100).toFixed(2),
      "UTC(NTSC)-GPST": (Math.random() * 200 - 100).toFixed(2),
      "UTC(NTSC)-GLNT": (Math.random() * 200 - 100).toFixed(2),
      "UTC(NTSC)-GST": (Math.random() * 200 - 100).toFixed(2),
    };
  });
};

onMounted(() => {
  generateTimeDiffList();
  // 每分钟更新星地融合授时偏差
  setInterval(generateTimeDiffList, 60000);
});
</script>

<style lang="scss" scoped>
.b2b-ppp-diff {
  .chart-content {
    height: 400px;
    margin-bottom: 20px;
  }

  .table-wrapper {
    background-color: #fff;
    border-radius: 4px;
    padding: 15px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .table-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 15px;
      color: #303133;
      padding-left: 5px;
      border-left: 4px solid #409EFF;
    }

    .pagination-container {
      margin-top: 15px;
      padding: 10px 0;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f8f9fa;
      border-radius: 4px;

      :deep(.el-pagination) {
        justify-content: center;
        font-size: 14px;

        .el-pagination__total,
        .el-pagination__sizes,
        .el-pagination__jump {
          font-size: 14px;
        }

        button {
          font-size: 14px;

          &:not(:disabled) {
            background-color: #fff;
            &:hover {
              color: #409EFF;
            }
          }
        }

        .el-pager li {
          font-size: 14px;
        }

        .el-pagination__sizes {
          margin-right: 15px;
        }

        .el-input__inner {
          height: 32px;
          font-size: 14px;
        }

        .el-pagination__jump {
          margin-left: 15px;
        }
      }
    }
  }
}
</style>
