import { SetupStoreId } from "@/enum";
import { defineStore } from "pinia";
import { computed, reactive, toRefs } from "vue";
import { subscribe, unsubscribe, unsubscribeAll, initScocket } from "@/api/ws";
import apiAjax from "@/api/index";
export const useHome = defineStore(
  SetupStoreId.Home,
  () => {
    // states
    const HomeData = reactive({
      navList: {
        timingData: [
          { code: "01", system: "UTC(NTSC)-BDT", value: null },
          { code: "02", system: "UTC(NTSC)-GPST", value: null },
          { code: "03", system: "UTC(NTSC)-GLNT", value: null },
          { code: "04", system: "UTC(NTSC)-GST", value: null },
        ],
        alertsList: [],
      },
      // 方法
      sectionFn: {
        // 星地融合授时信息
        fusion: async () => {},
        // 卫星伪码授时增强信息
        pseudoRange: async () => {},
        // 授时精度
        accuracy: async () => {},
      },
      activeTab: "fusion",
    });
    // 定时器
    let times = null;
    // 设置定时器
    const setTimes = (info = true) => {
      if (info) {
        times = setInterval(() => {
          HomeData.sectionFn[HomeData.activeTab]();
        }, 60000);
      } else {
        clearInterval(times);
        times = null;
      }
    };
    // 初始化
    const init = async () => {
      await initScocket();
      await getAlertsList();
      HomeData.sectionFn[HomeData.activeTab]();
      setTimes();
    };

    const getAlertsList = async () => {
      let { data } = await apiAjax.get(
        "/api/jnx/fusion/apps/satellite/home/<USER>/errorInfo/findLatest",
      );
      HomeData.navList.alertsList = data.map((i) => {
        return {
          id: i.id,
          message:
            i.timestamp +
            " 来源(" +
            i.resourceInstanceCode +
            ") " +
            i.errorDesc,
        };
      });

      subscribe("/topic/home/<USER>/errorInfo/findLatest", (data) => {
        let listData = data.map((i) => {
          return {
            id: i.id,
            message:
              i.timestamp +
              " 来源(" +
              i.resourceInstanceCode +
              ") " +
              i.errorDesc,
          };
        });
        listData = [...listData, ...HomeData.navList.alertsList];
        HomeData.navList.alertsList = listData.slice(0, 100);
      });
    };

    const changactiveTab = async () => {
      console.log(HomeData.activeTab);
      setTimes(false);
      await HomeData.sectionFn[HomeData.activeTab]();
      setTimes();
    };

    const setChartfn = (name, fn) => {
      HomeData.sectionFn[name] = fn;
    };
    // 设置timingData值
    const setNavListTimingData = (data) => {
      Object.keys(data).forEach((key) => {
        HomeData.navList.timingData.forEach((item) => {
          if (item.code == key) {
            let value = data[key][Object.keys(data[key])[0]];
            console.log(value);
            item.value = value;
          }
        });
      });
    };

    // getters

    // actions

    return {
      ...toRefs(HomeData),
      init,
      changactiveTab,
      setChartfn,
      setNavListTimingData
    };
  },
  {
    // 是否持久化到浏览器缓存localStorage
    persist: false,
  },
);
