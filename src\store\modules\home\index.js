import { SetupStoreId } from "@/enum";
import { defineStore } from "pinia";
import { computed, reactive, toRefs } from "vue";
import { subscribe, unsubscribe, unsubscribeAll, initScocket } from "@/api/ws";
import apiAjax from "@/api/index";
export const useHome = defineStore(
  SetupStoreId.Home,
  () => {
    // states
    const HomeData = reactive({
      navList: {
        timingData: [
          { code: "01", names: "UTC(NTSC)-BDT", value: null },
          { code: "02", names: "UTC(NTSC)-GPST", value: null },
          { code: "03", names: "UTC(NTSC)-GLNT", value: null },
          { code: "04", names: "UTC(NTSC)-GST", value: null },
        ],
        alertsList: [],
      },
      // 方法
      sectionFn: {
        // 星地融合授时信息
        fusion: async () => {},
        // 卫星伪码授时增强信息
        pseudoRange: async () => {},
        // 授时精度
        accuracy: async () => {},
      },
      activeTab: "fusion",
    });
    // 定时器
    let times = null;
    // 设置定时器 - 优化为每分钟30秒时定点刷新
    const setTimes = (info = true) => {
      if (info) {
        // 清除之前的定时器
        if (times) {
          clearInterval(times);
          times = null;
        }

        // 计算到下一个30秒的延迟时间
        const now = new Date();
        const currentSeconds = now.getSeconds();
        const currentMilliseconds = now.getMilliseconds();

        // 计算到30秒的延迟时间
        let delayToNext30s;
        if (currentSeconds < 30) {
          // 当前秒数小于30，等待到30秒
          delayToNext30s = (30 - currentSeconds) * 1000 - currentMilliseconds;
        } else {
          // 当前秒数大于等于30，等待到下一分钟的30秒
          delayToNext30s = (90 - currentSeconds) * 1000 - currentMilliseconds;
        }

        // 首次延迟执行到30秒
        setTimeout(() => {
          // 立即执行一次
          HomeData.sectionFn[HomeData.activeTab]();

          // 然后每60秒执行一次（每分钟的30秒）
          times = setInterval(() => {
            HomeData.sectionFn[HomeData.activeTab]();
          }, 60000);
        }, delayToNext30s);

      } else {
        clearInterval(times);
        times = null;
      }
    };
    // 初始化
    const init = async () => {
      await initScocket();
      await getAlertsList();
      HomeData.sectionFn[HomeData.activeTab]();
      setTimes();
    };

    const getAlertsList = async () => {
      let { data } = await apiAjax.get(
        "/api/jnx/fusion/apps/satellite/home/<USER>/errorInfo/findLatest",
      );
      HomeData.navList.alertsList = data.map((i) => {
        return {
          id: i.id,
          message:
            i.timestamp +
            " 来源(" +
            i.resourceInstanceCode +
            ") " +
            i.errorDesc,
        };
      });

      subscribe("/topic/home/<USER>/errorInfo/findLatest", (data) => {
        let listData = data.map((i) => {
          return {
            id: i.id,
            message:
              i.timestamp +
              " 来源(" +
              i.resourceInstanceCode +
              ") " +
              i.errorDesc,
          };
        });
        listData = [...listData, ...HomeData.navList.alertsList];
        HomeData.navList.alertsList = listData.slice(0, 100);
      });
    };

    const changactiveTab = async () => {
      console.log(HomeData.activeTab);
      setTimes(false);
      await HomeData.sectionFn[HomeData.activeTab]();
      setTimes();
    };

    const setChartfn = (name, fn) => {
      HomeData.sectionFn[name] = fn;
    };
    // 设置timingData值
    const setNavListTimingData = (data) => {
      Object.keys(data).forEach((key) => {
        HomeData.navList.timingData.forEach((item) => {
          if (item.code == key) {
            let value = data[key][Object.keys(data[key])[0]];
            console.log(value);
            item.value = value;
          }
        });
      });
    };

    // getters

    // actions

    return {
      ...toRefs(HomeData),
      init,
      changactiveTab,
      setChartfn,
      setNavListTimingData
    };
  },
  {
    // 是否持久化到浏览器缓存localStorage
    persist: false,
  },
);
