<template>
  <div class="transceiver-container">
    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="导航系统">
          <el-select
            style="width: 180px"
            v-model="searchForm.navigationSystemCode"
            placeholder="请选择系统"
            @change="handleSystemChange"
          >
            <el-option
              v-for="item in systemOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="卫星PRN">
          <el-select
            style="width: 180px"
            v-model="searchForm.satellitePrn"
            placeholder="请选择PRN"
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in prnOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>



        <el-form-item label="时间">
          <el-date-picker
            style="width: 300px"
            v-model="searchForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :disabled-date="configure.disabledDate"
            :shortcuts="configure.dateShortcuts"
          />
        </el-form-item>
        <el-form-item label="数据来源">
          <el-select
            style="width: 160px"
            v-model="searchForm.dataSource"
            placeholder="请选择数据来源"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSerch" :loading="loading">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table
      :data="tableData"
      class="table-border"
      border
      style="width: 100%"
      v-loading="loading"
      :header-cell-style="headerCellStyle"
      :cell-style="cellStyle"
    >
      <el-table-column type="index" label="序号" width="60" />
      <el-table-column prop="timestamp" label="时间" width="180" />
      <el-table-column prop="resourceTypeName" label="数据来源" width="280">
        <template #default="scope">
          {{
            scope.row.resourceTypeName + `(${scope.row.resourceInstanceCode})`
          }}
        </template>
      </el-table-column>
      <el-table-column prop="satellitePrn" label="卫星号" />
      <el-table-column prop="gdgzlysk" label="历元时刻(s)" />
      <el-table-column prop="iodSsr" label="IOD SSR" />
      <el-table-column prop="iodn" label="IODN" />

      <el-table-column prop="gdmjxgzz" label="径向改正数(m)" >
        <template #default="scope">
          <!--  这个要改 -->
          {{ toScientificNotation(scope.row.gdmjxgzz) }}
        </template>
      </el-table-column>
      <el-table-column prop="gdmqxgzz" label="切向改正数(m)" >
        <template #default="scope">
          {{ toScientificNotation(scope.row.gdmqxgzz) }}
        </template>
      </el-table-column>
      <el-table-column prop="gdmfxgzz" label="法向改正数(m)" >
        <template #default="scope">
          {{ toScientificNotation(scope.row.gdmfxgzz) }}
        </template>
      </el-table-column>
      <el-table-column prop="uraClass" label="URAclass" />
      <el-table-column prop="uraValue" label="URAvalue" />
      <el-table-column prop="iodc" label="IODC" />
      <el-table-column prop="clockCorrNum" label="钟差改正数(m)" >
        <template #default="scope">
          {{ toScientificNotation(scope.row.clockCorrNum) }}
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[20, 30, 50]"
        layout="sizes, total, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import configure from "@/utils/configure.js";
import apiAjax from "@/api/index";
import { useAllSatellite } from "@/store/modules/allSatellite";
const allSatelliteStore = useAllSatellite();
// 表头样式
const headerCellStyle = {
  background: "#f5f7fa",
  color: "#606266",
  fontWeight: "bold",
  fontSize: "14px",
  height: "50px",
};

// 单元格样式
const cellStyle = {
  fontSize: "16px",
  padding: "10px 0",
};
const typeOptions = [
  { label: "全部", value: "" },
  { label: "SAM1", value: "SAM1" },
  { label: "SAM2", value: "SAM2" },
  { label: "SAM3", value: "SAM3" },
];

const seturlQuery = () => {
  const postData = {
    ...searchForm,
    page: currentPage.value,
    size: pageSize.value,
  };

  // 处理时间范围
  if (searchForm.timeRange?.length === 2) {
    postData.beginTime = configure.formatDate(
      new Date(`${searchForm.timeRange[0]} 00:00:00`),
    );
    postData.endTime = configure.formatDate(
      new Date(`${searchForm.timeRange[1]} 23:59:59`),
    );
  }
  delete postData.timeRange;

  // 构建查询字符串
  return Object.entries(postData)
    .filter(
      ([_, value]) => value !== undefined && value !== null && value !== "",
    )
    .map(([key, value]) => {
      // 时间相关参数不进行URL编码，其他参数正常编码
      if (key === "beginTime" || key === "endTime") {
        return `${encodeURIComponent(key)}=${value}`;
      } else {
        return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
      }
    })
    .join("&");
};

// 将数字转换为科学计数法
const toScientificNotation = (numStr) => {
  const num = parseFloat(numStr);
  return num.toExponential(3);
};



const onSerch = async () => {
  loading.value = true;
  try {
    const prosm = seturlQuery();
    let { data } = await apiAjax.get(
      "/api/jnx/fusion/apps/satellite/collect/elecParam/b2b/findB2bcorrPage?" +
        prosm,
    );
    tableData.value = data.content || [];
    total.value = data.totalElements || 0;
  } catch (error) {
    console.error("查询数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 搜索表单
const searchForm = reactive({
  timeRange: [],
  dataSource: "",
  navigationSystemCode: "",
  satellitePrn: "",
});

// 导航系统选项
const systemOptions = computed(() => {
  if (!allSatelliteStore.data || !allSatelliteStore.data.length) return [];
  return allSatelliteStore.data.map((system) => ({
    value: system.systemCode,
    label: system.systemName,
  }));
});

// 处理导航系统变更
const handleSystemChange = () => {
  // 清空PRN选择
  searchForm.satellitePrn = "";
  // // 重新加载数据
  // currentPage.value = 1;
  // loadData();
};

// 卫星PRN码选项
const prnOptions = computed(() => {
  if (!searchForm.navigationSystemCode || !allSatelliteStore.data) return [];

  // 找到选中的导航系统
  const selectedSystem = allSatelliteStore.data.find(
    (system) => system.systemCode === searchForm.navigationSystemCode,
  );

  if (!selectedSystem || !selectedSystem.satelliteInfos) return [];

  // 返回该系统下的所有卫星PRN
  return selectedSystem.satelliteInfos.map((satellite) => ({
    value: satellite.prn,
    label: satellite.prn,
  }));
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);
const loading = ref(false);

// 表格数据
const tableData = ref([]);

// 分页方法
const handleSizeChange = (val) => {
  currentPage.value = 1;
  pageSize.value = val;
  onSerch();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  onSerch();
};
onMounted(async () => {
  await allSatelliteStore.initAllData();
  searchForm.navigationSystemCode = allSatelliteStore.data[0].systemCode;
  onSerch();
});
</script>

<style scoped lang="scss">
.transceiver-container {
  margin-top: 16px;
  padding: 16px;
  background: #fff;
  .search-area {
    margin-bottom: 16px;
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
@media only screen and (max-height: 1080px) {
  .table-border {
    height: 830px;
  }
}
@media only screen and (max-height: 919px) {
  .table-border {
    height: 680px;
  }
}
</style>
