<template>
  <div class="satellite-accuracy">
    <div class="chart-section">
      <div class="chart-header">
        <div class="header-main">
          <div class="title-wrapper">
            <i class="el-icon-data-analysis" />
            <span class="chart-title">系统授时精度</span>
          </div>
          <div class="time-control">
            <el-button type="text" :disabled="!canGoPrev" @click="handlePrevHour">
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            <span class="time-range">{{currentTimeRange}}</span>
            <el-button type="text" :disabled="!canGoNext" @click="handleNextHour">
              <el-icon><ArrowRight /></el-icon>
            </el-button>
            <el-button type="success" @click="viewTrend">查看趋势图</el-button>
          </div>
        </div>
        <div class="legend-info">
          <div class="info-title">授时精度等级说明：</div>
          <div class="info-content">
            <div class="info-item" v-for="(range, level) in accuracyLevels" :key="level">
              <span class="level">{{level}}级：</span>
              <span class="range">{{range}}</span>
            </div>
          </div>
        </div>
        <div class="title-divider"></div>
      </div>
    </div>
    <div class="table-section">
      <div class="table-header">
        <div class="title-wrapper">
          <i class="el-icon-time" />
          <span class="table-title">单星授时精度</span>
        </div>
        <div class="title-divider"></div>
      </div>
      <el-table
        :data="accuracyList"
        border
        style="width: 100%"
        height="600"
        :header-cell-style="headerCellStyle"
        :cell-style="cellStyle"
        :row-class-name="tableRowClassName"
        stripe
        highlight-current-row
      >
        <el-table-column prop="timeRange" label="时间段" />
        <el-table-column prop="satelliteId" label="卫星号" />
        <el-table-column prop="frequency" label="频点" />
        <el-table-column prop="timeDiff" label="时差(ns)" />
        <el-table-column prop="accuracyLevel" label="授时等级" />
      </el-table>
    </div>

    <!-- 趋势图弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="系统授时精度趋势图"
      width="80%"
      :before-close="() => dialogVisible = false"
    >
      <div class="chart-content">
        <bar-chart :data="lineChartOption" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import BarChart from "@/components/lineChart/lineChart.vue";
import { ArrowLeft, ArrowRight } from '@element-plus/icons-vue'

const systems = ["BDS", "GPS", "GLONASS", "GALILEO"];
const types = ["卫星伪码授时偏差", "载波相位授时偏差", "B2b PPP时差"];

// 生成图表数据
const lineChartOption = ref({
  xAxis: {
    type: "category",
    data: systems,
  },
  yAxis: {
    type: "value",
    name: "授时等级",
    max: 7,
  },
  legend: {
    bottom: 10,
    data: types,
  },
  tooltip: {
    trigger: "axis",
  },
  series: types.map((type, index) => ({
    name: type,
    type: "bar",
    data: systems.map(() => Math.floor(Math.random() * 8)),
    label: {
      show: true,
      position: "top",
    },
    itemStyle: {
      color: ["#3B8CFF", "#FFA07A", "#4CAF50"][index],
    },
    barWidth: "15%",
    barGap: "30%",
  })),
}); // 分页相关
const currentPage = ref(1);
const pageSize = ref(10);
const handleSizeChange = (val) => {
  pageSize.value = val;
};
const handleCurrentChange = (val) => {
  currentPage.value = val;
};

// 生成授时精度列表数据
const accuracyList = ref([]);
const generateAccuracyList = () => {
  const now = new Date();
  const satellites = [
    ...Array.from(
      { length: 63 },
      (_, i) => `C${String(i + 1).padStart(2, "0")}`,
    ),
    ...Array.from(
      { length: 32 },
      (_, i) => `G${String(i + 1).padStart(2, "0")}`,
    ),
    ...Array.from(
      { length: 27 },
      (_, i) => `R${String(i + 1).padStart(2, "0")}`,
    ),
    ...Array.from(
      { length: 30 },
      (_, i) => `E${String(i + 1).padStart(2, "0")}`,
    ),
  ];

  accuracyList.value = Array.from({ length: 20 }, (_, i) => {
    const startTime = new Date(now - i * 3600000);
    const endTime = new Date(startTime.getTime() + 3600000);
    return {
      timeRange: `${startTime.toLocaleTimeString("zh-CN")} - ${endTime.toLocaleTimeString("zh-CN")}`,
      satelliteId: satellites[Math.floor(Math.random() * satellites.length)],
      frequency: `B${Math.floor(Math.random() * 3) + 1}`,
      timeDiff: (Math.random() * 200 - 100).toFixed(2),
      accuracyLevel: Math.floor(Math.random() * 8),
    };
  });
};

onMounted(() => {
  generateAccuracyList();
  // 每小时更新授时精度列表
  setInterval(generateAccuracyList, 3600000);
});

// 添加授时精度等级说明数据
const accuracyLevels = {
  "0": "0~5ns",
  "1": "5~10ns",
  "2": "10~20ns",
  "3": "20~30ns",
  "4": "30~50ns",
  "5": "50~100ns",
  "6": "100~1000ns",
  "7": ">1000ns"
};

// 添加时间控制相关
const currentHour = ref(new Date().getHours());
const currentDate = ref(new Date());

// 计算UTC时间范围显示
const currentTimeRange = computed(() => {
  const date = currentDate.value;
  const hour = currentHour.value;

  const startTime = new Date(date);
  startTime.setUTCHours(hour, 0, 0, 0);

  const endTime = new Date(startTime);
  endTime.setUTCHours(hour + 1, 0, 0, 0);

  return `${startTime.toISOString().replace('T', ' ').slice(0, -5)} ~ ${endTime.toISOString().replace('T', ' ').slice(0, -5)}`;
});

// 控制按钮禁用状态
const canGoPrev = computed(() => {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const currentDateTime = new Date(currentDate.value);
  currentDateTime.setHours(currentHour.value);

  // 限制只能查看当天的数据
  return currentDateTime > today;
});

const canGoNext = computed(() => {
  const now = new Date();
  const currentDateTime = new Date(currentDate.value);
  currentDateTime.setHours(currentHour.value);

  // 不能超过当前时间
  return currentDateTime.getTime() + 3600000 <= now.getTime();
});

// 处理时间切换
const handlePrevHour = () => {
  if (currentHour.value === 0) {
    currentDate.value = new Date(currentDate.value.getTime() - 24 * 3600000);
    currentHour.value = 23;
  } else {
    currentHour.value--;
  }
  updateData();
};

const handleNextHour = () => {
  if (currentHour.value === 23) {
    currentDate.value = new Date(currentDate.value.getTime() + 24 * 3600000);
    currentHour.value = 0;
  } else {
    currentHour.value++;
  }
  updateData();
};

// 更新数据方法
const updateData = () => {
  // 这里添加获取指定时间段数据的逻辑
  generateAccuracyList();
  // 更新图表数据
  lineChartOption.value.series = types.map((type, index) => ({
    name: type,
    type: "bar",
    data: systems.map(() => Math.floor(Math.random() * 8)),
    label: {
      show: true,
      position: "top",
    },
    itemStyle: {
      color: ["#3B8CFF", "#FFA07A", "#4CAF50"][index],
    },
    barWidth: "15%",
    barGap: "30%",
  }));
};

// 表头样式
const headerCellStyle = {
  background: "#f5f7fa",
  color: "#606266",
  fontWeight: "bold",
  fontSize: "18px",
  height: "50px",
};

// 单元格样式
const cellStyle = {
  fontSize: "16px",
  padding: "10px 0",
};

// 添加表格行样式
const tableRowClassName = ({ rowIndex }) => {
  return rowIndex % 2 === 0 ? 'even-row' : 'odd-row';
};

// 弹窗相关
const dialogVisible = ref(false);

// 查看趋势图
const viewTrend = () => {
  dialogVisible.value = true;
};
</script>

<style lang="scss" scoped>
.satellite-accuracy {
  .chart-section {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;

    .chart-header {
      margin-bottom: 20px;

      .header-main {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .time-control {
          display: flex;
          align-items: center;
          gap: 8px;

          .time-range {
            font-size: 14px;
            color: #606266;
            min-width: 100px;
            text-align: center;
          }

          .el-button {
            padding: 0;
            height: 32px;
            width: 32px;
            display: flex;
            align-items: center;
            justify-content: center;

            &:not(:disabled) {
              color: #409EFF;

              &:hover {
                color: #66b1ff;
                background-color: #f5f7fa;
              }
            }

            &:disabled {
              color: #c0c4cc;
            }

            .el-icon {
              font-size: 16px;
            }
          }
        }
      }

      .title-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;

        i {
          color: #409EFF;
          font-size: 18px;
        }

        .chart-title {
          font-size: 16px;
          font-weight: 600;
          color: #1f2f3d;
        }
      }

      .legend-info {
        margin: 12px 0;
        padding: 12px;
        background: #f8f9fa;
        border-radius: 4px;

        .info-title {
          color: #606266;
          font-size: 14px;
          margin-bottom: 8px;
        }

        .info-content {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
          gap: 8px;

          .info-item {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 13px;

            .level {
              color: #606266;
              white-space: nowrap;
            }

            .range {
              color: #909399;
            }
          }
        }
      }

      .title-divider {
        margin-top: 8px;
        height: 2px;
        background: linear-gradient(90deg, #409EFF 0%, rgba(64, 158, 255, 0.2) 100%);
        border-radius: 1px;
      }
    }
  }

  .table-section {
    margin-top: 20px;
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .table-header {
      margin-bottom: 20px;

      .title-wrapper {
        display: flex;
        align-items: center;
        gap: 8px;

        i {
          color: #409EFF;
          font-size: 18px;
        }

        .table-title {
          font-size: 16px;
          font-weight: 600;
          color: #1f2f3d;
        }
      }

      .title-divider {
        margin-top: 8px;
        height: 2px;
        background: linear-gradient(90deg, #409EFF 0%, rgba(64, 158, 255, 0.2) 100%);
        border-radius: 1px;
      }
    }
  }
}
</style>
