<template>
  <div class="fusion-info">
    <!-- 添加查询表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm">

        <el-form-item label="时间">
          <el-date-picker
            style="width: 300px"
            v-model="searchForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :disabled-date="configure.disabledDate"
            :shortcuts="configure.dateShortcuts"
          />
        </el-form-item>
        <el-form-item label="数据来源">
          <el-select
            style="width: 160px"
            v-model="searchForm.dataSource"
            placeholder="请选择数据来源"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch" :loading="loading">查询</el-button>
          <el-button type="success" @click="viewTrend">查看趋势图</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 导航系统选择按钮 -->
    <div class="system-buttons">
      <el-button-group>
        <el-button
          :type="activeSystem === 'BDS' ? 'primary' : 'default'"
          @click="switchSystem('BDS')">BDS</el-button>
        <el-button
          :type="activeSystem === 'GPS' ? 'primary' : 'default'"
          @click="switchSystem('GPS')">GPS</el-button>
        <el-button
          :type="activeSystem === 'GLONASS' ? 'primary' : 'default'"
          @click="switchSystem('GLONASS')">GLONASS</el-button>
        <el-button
          :type="activeSystem === 'Galileo' ? 'primary' : 'default'"
          @click="switchSystem('Galileo')">Galileo</el-button>
      </el-button-group>
    </div>

    <div class="tables-section">
      <div class="table-wrapper">
        <el-table
          :data="currentTableData"
          border
          style="width: 1650px;"
          height="65vh"
          v-loading="loading"
          :header-cell-style="headerCellStyle"
          :cell-style="cellStyle"
        >
          <!-- 时间列 固定不变 -->
          <el-table-column prop="timestamp" label="时间" width="180" />
          <el-table-column prop="resourceTypeName" label="数据来源" width="280">
        <template #default="scope">
          {{
            scope.row.resourceInstanceName + `(${scope.row.resourceInstanceCode})`
          }}
        </template>
      </el-table-column>
          <!-- 动态表头列 -->
          <el-table-column
            v-for="(header, index) in dynamicHeaders"
            :key="index"
            :prop="header.prop"
            :label="header.label"
          />
        </el-table>
        <!-- 添加分页器 -->
        <div class="pagination-wrapper">
          <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="sizes, total, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 趋势图弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="数据趋势图"
      width="80%"
      :before-close="() => (dialogVisible = false)"
    >
      <div class="chart-content">
        <line-chart :data="lineChartOption" />
        <div class="system-select">
          <span>频点：</span>
          <el-select
            v-model="selectedSystem"
            placeholder="请选择系统"
            clearable
          >
            <el-option label="B1I" value="B1I"></el-option>
            <el-option label="B3I" value="B1C"></el-option>
            <el-option label="B1C" value="B2a"></el-option>
            <el-option label="B2a" value="B2b"></el-option>
          </el-select>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed, watch } from "vue";
import configure from "@/utils/configure.js";
import apiAjax from "@/api/index";
import { useAllSatellite } from "@/store/modules/allSatellite";
const allSatelliteStore = useAllSatellite();

// 当前激活的导航系统
const activeSystem = ref('BDS');

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);

// 搜索表单
const searchForm = reactive({
  dataSource: "",
  timeRange: [],
});
const typeOptions = [
  { label: "全部", value: "" },
  { label: "SAM1", value: "SAM1" },
  { label: "SAM2", value: "SAM2" },
  { label: "SAM3", value: "SAM3" },
];
// 分页配置
const currentPage = ref(1);
const pageSize = ref(10);

// 表头样式
const headerCellStyle = {
  background: "#f5f7fa",
  color: "#606266",
  fontWeight: "bold",
  fontSize: "18px",
  height: "50px",
};

// 单元格样式
const cellStyle = {
  fontSize: "16px",
  padding: "10px 0",
};

// 系统数据映射表
const systemMapping = {
  'BDS': 'bdsList',
  'GPS': 'gpsList',
  'GLONASS': 'glonassList',
  'Galileo': 'galileoList'
};

// 获取系统对应的频率组合列表
const getFrequencyCombinations = (systemName) => {
  if (!allSatelliteStore.data || !allSatelliteStore.data.length) return [];

  const system = allSatelliteStore.data.find(item => item.systemName === systemName);
  return system ? system.frequencyCombinations : [];
};

// 动态表头
const dynamicHeaders = computed(() => {
  const freqCombinations = getFrequencyCombinations(activeSystem.value);

  return freqCombinations.map((freq, index) => ({
    label: freq.frequencyId+"(ns)",
    prop: `frequencyBias_${index}` // 使用索引创建唯一属性名
  }));
});

// 处理当前表格数据
const currentTableData = computed(() => {
  if (!tableData.value.length) return [];

  const systemList = systemMapping[activeSystem.value];

  return tableData.value.map(item => {
    const result = {
      timestamp: item.timestamp,
      resourceInstanceName: item.resourceInstanceName,
      resourceTypeCode: item.resourceTypeCode,
      resourceInstanceCode: item.resourceInstanceCode,
    };

    // 如果存在对应系统的数据列表
    if (item[systemList] && Array.isArray(item[systemList])) {
      item[systemList].forEach((freq, index) => {
        result[`frequencyBias_${index}`] = freq.frequencyBias;
      });
    }

    return result;
  });
});

// 切换导航系统
const switchSystem = (system) => {
  activeSystem.value = system;
};

// 构建查询参数
const buildQueryParams = (page, size) => {
  const params = new URLSearchParams();

  // 时间范围参数
  if (searchForm.timeRange?.length === 2) {
    params.append(
      "beginTime",
      configure.formatDate(new Date(`${searchForm.timeRange[0]} 00:00:00`)),
    );
    params.append(
      "endTime",
      configure.formatDate(new Date(`${searchForm.timeRange[1]} 23:59:59`)),
    );
  }

  // 分页参数
  params.append("page", page);
  params.append("size", size);
  if (searchForm.dataSource) {
    params.append("dataSource", searchForm.dataSource);
  }

  return params.toString();
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const queryString = buildQueryParams(currentPage.value, pageSize.value);
    const { data: response } = await apiAjax.get(
      `/api/jnx/fusion/apps/satellite/collect/timeDiff/findXtwmsspcPage?${queryString}`,
    );
    console.log(response);
    if (response) {
      tableData.value = response.content || [];
      total.value = response.totalElements || 0;
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理函数
const onSearch = () => {
  currentPage.value = 1;
  loadData();
};

// 分页处理函数
const handleSizeChange = (val) => {
  currentPage.value = 1;
  pageSize.value = val;
  loadData();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;

  loadData();
};

// 弹窗相关
const dialogVisible = ref(false);

// 图表配置
const lineChartOption = ref({
  xAxis: {
    type: "category",
    data: Array.from(
      { length: 24 },
      (_, i) => `${String(i).padStart(2, "0")}:00`,
    ),
  },
  yAxis: {
    type: "value",
    name: "时差(ns)",
  },
  legend: {
    bottom: 10,
    data: [
      "UTC(NTSC)-BDT",
      "UTC(NTSC)-GPST",
      "UTC(NTSC)-GLNT",
      "UTC(NTSC)-GST",
    ],
  },
  grid: {
    top: "30px",
    left: "50px",
    right: "30px",
    bottom: "60px",
  },
  tooltip: {
    trigger: "axis",
  },
  series: [
    {
      name: "UTC(NTSC)-BDT",
      data: Array.from({ length: 24 }, () =>
        (Math.random() * 200 - 100).toFixed(1),
      ),
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: { color: "#3B8CFF" },
      lineStyle: { width: 2 },
    },
    {
      name: "UTC(NTSC)-GPST",
      data: Array.from({ length: 24 }, () =>
        (Math.random() * 200 - 100).toFixed(1),
      ),
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: { color: "#FFA07A" },
      lineStyle: { width: 2 },
    },
    {
      name: "UTC(NTSC)-GLNT",
      data: Array.from({ length: 24 }, () =>
        (Math.random() * 200 - 100).toFixed(1),
      ),
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: { color: "#4CAF50" },
      lineStyle: { width: 2 },
    },
    {
      name: "UTC(NTSC)-GST",
      data: Array.from({ length: 24 }, () =>
        (Math.random() * 200 - 100).toFixed(1),
      ),
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: { color: "#FF6347" },
      lineStyle: { width: 2 },
    },
  ],
});

// 查看趋势图
const viewTrend = () => {
  dialogVisible.value = true;
};

// 初始加载数据
onMounted(async () => {
  await allSatelliteStore.initAllData();
  console.log(allSatelliteStore.data);
  loadData();
});
</script>

<style lang="scss" scoped>
.fusion-info {
  position: relative;
  .search-form {
    background-color: #fff;
    padding: 12px;
    border-radius: 4px;
    // margin-bottom: 16px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .system-buttons {
    background-color: #fff;
    padding: 12px;
    border-radius: 4px;
    margin-bottom: 16px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 0;
    left: 0;
  }

  .chart-content {
    height: 420px;
    margin-bottom: 20px;
    position: relative;
    .system-select {
      display: flex;
      align-items: center;
      position: absolute;
      top: 0;
      width: 200px;
      right: 10px;
      span {
        width: 70px;
      }
    }
  }

  .tables-section {
    display: flex;
    gap: 20px;

    .table-wrapper {
      flex: 1;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .table-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #303133;
        padding-left: 5px;
        border-left: 4px solid #409eff;
      }

      .pagination-wrapper {
        margin-top: 16px;
        padding: 10px;
        display: flex;
        justify-content: flex-end;
        background-color: #fff;
      }
    }
  }
}
</style>
