<template>
  <div class="oriData_nav">
    <div
      v-for="item in props.navList"
      :key="item.id"
      @click="eimt('setoriData', item.id)"
      :class="{
        oriData_nav_item: true,
        oriData_nav_info: props.index == item.id,
      }"
    >
      <el-icon><Tickets /></el-icon><span>{{ item.name }}</span>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from "vue";
const props = defineProps({
  navList: {
    type: Array,
    default: () => [],
  },
  index: {
    type: Number,
    default: 1,
  },
});
const eimt = defineEmits();
</script>

<style lang="scss" scoped>
.oriData_nav {
  background: #ffffff;
  display: flex;
  padding: 12px 16px;
  border-radius: 6px;
  .oriData_nav_item {
    margin-right: 20px;
    display: flex;
    align-items: center;
    padding: 5px 12px;
    cursor: pointer;
  }
  .oriData_nav_info {
    background: #ecf2fe;
    border-radius: 3px;
    color: #0052d9;
  }
}
</style>
