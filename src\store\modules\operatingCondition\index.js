import { SetupStoreId } from '@/enum';
import { defineStore } from 'pinia';
import { reactive, toRefs } from 'vue';
import apiAjax from "@/api/index";
import { fillBlanks } from './data.js';
import { subscribe, unsubscribe, unsubscribeAll } from "@/api/ws";
/**
 * Home 模块的状态管理
 * @returns {Object} store对象
 */
export const useOperatingCondition = defineStore(
  SetupStoreId.OperatingCondition,
  () => {
    // 状态定义
    const homeData = reactive({
      // 当前选中的机柜设备
      selectedCabinets: {
        lineChartData: []
      },
      // 设备的状态
      equipmentStatus: {
        1: { title: "正常", class: 'normal', color: "#00A870" },
        2: { title: "离线", class: 'fault', color: '#fde047' },
        3: { title: "警告", class: 'fault', color: '#fde047' },
        4: { title: "异常", class: 'abnormal', color: '#c9353f' },
        5: { title: "故障", class: 'abnormal', color: '#c9353f' }
      },
      // 机柜状态
      cabinetStatus: {
        0: { title: "正常", class: 'normal', color: "#00A870" },
        1: { title: "警告", class: 'fault', color: '#fde047' },
        2: { title: "异常", class: 'abnormal', color: '#c9353f' }
      },
      // 选中机柜的详细信息
      selectedCabinetsItem: {
        info: false, // 控制显示模式：true-显示单个设备信息，false-显示机柜信息
        cabBox: {
          id: false
        }, // 机柜信息
      },
      // 机柜列表
      cabinetsList: []
    });

    /**
     * 初始化数据
     * 获取机柜信息并处理状态
     */
    const init = async () => {
      let { data } = await apiAjax.get("/api/jnx/fusion/apps/satellite/gkInfo/getCabinetView");
      updateElectedCabinetsItem(data);
      subscribe("/topic/cabinetView/cabinet/" + data.id, (data) => {
        updateElectedCabinetsItem(data);
      })

    };


    // 处理机柜中的设备状态
    const updateElectedCabinetsItem = (newAllWorkStatus) => {
      let item = newAllWorkStatus
      let newSelected = fillBlanks(item.resources);
      item.newResources = newSelected;

      // 确保 resources 数组存在且倒序前先创建副本
      const resourcesCopy = [...(newAllWorkStatus.resources || [])];
      resourcesCopy.reverse();

      // 设置 title 属性，如果没有则使用 name
      item.title = item.name || "机柜信息";

      // 设置默认状态为正常
      item.cabinetStatus = homeData.cabinetStatus[0];

      // 统计设备状态
      item.lineChartData = summarizeStatus(newAllWorkStatus.resources || []);

      // 处理设备状态显示 (列表)
      item.allList = resourcesCopy.map((j) => {
        // 确保每个设备有状态信息
        j.equipmentStatus = homeData.equipmentStatus[j.workStatus] || homeData.equipmentStatus[1];
        return j;
      });

      // 设置设备总状态
      item.equipmentStatus = homeData.equipmentStatus[1]; // 默认正常

      homeData.selectedCabinets = item;
    }
    /**
     * 处理设备选择事件
     * @param {Object} i 设备信息
     * @param {string} type 选择类型
     */
    const handleSelect = async (i, type) => {
      if (homeData.selectedCabinetsItem.cabBox.id) {
        unsubscribe("/topic/gkInfo/resource/" + homeData.selectedCabinetsItem.cabBox.id)
      }
      const objType = {
        // 子组件点击处理
        cabinetItem: async () => {
          let { data: newAllWorkStatus } = await apiAjax.get("/api/jnx/fusion/apps/satellite/gkInfo/getGkInfo/" + i.id);
          homeData.selectedCabinetsItem = { info: true, ...newAllWorkStatus, cabBox: { ...i } }
          // 订阅设备状态变化
          subscribe("/topic/gkInfo/resource/" + homeData.selectedCabinetsItem.cabBox.id, (data) => {
            homeData.selectedCabinetsItem = { info: true, ...newAllWorkStatus, cabBox: { ...homeData.selectedCabinetsItem.cabBox, ...data } }
          })
        },

      }
      setTimeout(async () => {
        await objType[type]()
      })
    }

    /**
     * 处理空白区域点击
     */
    const handleBlank = () => {
      homeData.selectedCabinetsItem.info = false;
      // 取消设备的订阅
      if (homeData.selectedCabinetsItem.cabBox.id) {
        unsubscribe("/topic/gkInfo/resource/" + homeData.selectedCabinetsItem.cabBox.id)
      }
    }

    /**
     * 统计设备状态数量
     * @param {Array} data 设备数据
     * @returns {Array} 统计结果
     */
    function summarizeStatus(data) {
      const statusMap = {
        1: "正常",
        2: "离线",
        3: "异常",
        4: "异常",
        5: "异常"
      };

      const statusCount = {
        正常: 0,
        离线: 0,
        异常: 0
      };

      // 防止 data 不是数组或为空
      if (!Array.isArray(data)) {
        return [
          { value: 0, name: "正常" },
          { value: 0, name: "离线" },
          { value: 0, name: "异常" }
        ];
      }

      data.forEach(item => {
        const statusName = statusMap[item.workStatus];
        if (statusName) {
          statusCount[statusName]++;
        }
      });

      return [
        { value: statusCount.正常, name: "正常" },
        { value: statusCount.离线, name: "离线" },
        { value: statusCount.异常, name: "异常" }
      ];
    }

    // 取消所有订阅
    const cleanup = () => {
      unsubscribeAll()
    }

    // 返回store对象
    return {
      ...toRefs(homeData),
      init,
      handleSelect,
      handleBlank,
      cleanup
    };
  },
  {
    persist: false, // 是否持久化到localStorage
  }
);
