import { reactive } from 'vue'
import Cabinet from "@/assets/device/机柜一.png"
import panel from "@/assets/device/面板.png"
import jksb1 from '@/assets/device/设备监控一.png'
import jksb2 from '@/assets/device/设备监控二.png'
import jksb3 from '@/assets/device/设备监控三.png'
import dunHuangImg from '@/assets/device/敦煌机柜.png'
import HAA from "@/assets/device/增强新罗兰检测接收机.png"
import HAB from "@/assets/device/频率比对测量设备.png"
import HAC from "@/assets/device/多通道时间间隔计数器.png"
import HAD from "@/assets/device/时间综合测量仪.png"
import HAE from "@/assets/device/网络时间服务器.png"
import HAF from "@/assets/device/频谱干扰测试系统.png"
import HAG from "@/assets/device/脉冲分配放大器.png"
import HAH from "@/assets/device/频率分配放大器.png"
import HAJ from "@/assets/device/数字记录仪.png"
import HAM from "@/assets/device/标准时间复现设备.png"
import HAN from "@/assets/device/网络连接服务器.png"
import HAO from "@/assets/device/网络交换机.png"
import HAP from "@/assets/device/工控机.png"
import HAQ from "@/assets/device/时间间隔计数器.png"
import HAR from "@/assets/device/PDU.png"
import HAS from "@/assets/device/低噪声电源.png"
import HAT from "@/assets/device/显示器.png"
import HAU from "@/assets/device/鼠标键盘.png"
import HAZ from "@/assets/device/硬盘录像机.png"
import HAY from "@/assets/device/kvm.png"
import HAX from "@/assets/device/鼠标键盘.png"

import HAV from "@/assets/device/北斗定位接收机.png"
import HBS from "@/assets/device/时间间隔计数器.png"


const imgMap = {
  Cabinet: Cabinet,
  panel: panel,
  jksb1: jksb1,
  jksb2: jksb2,
  jksb3: jksb3,
  dunHuangImg: dunHuangImg,
  HAA: HAA,
  HAB: HAB,
  HAC: HAC,
  HAD: HAD,
  HAE: HAE,
  HAF: HAF,
  HAG: HAG,
  HAH: HAH,
  HAJ: HAJ,
  HAM: HAM,
  HAN: HAN,
  HAO: HAO,
  HAP: HAP,
  HAQ: HAQ,
  HAR: HAR,
  HAS: HAS,
  HAT: HAT,
  HAU: HAU,
  HAZ: HAZ,
  HAY: HAY,
  HAX: HAX,
  HAV: HAV,
  HBS: HBS
}
// 黑名单  有写点击不让他去请求数据 问题是没东西
const banList = ['HAU', 'HAT']
// 设置机柜设备数据
export function fillBlanks(newItems) {
  const totalSlots = 38; // 总共的格子数
  let currentIndex = 0; // 当前处理到的格子索引
  const filledData = []; // 填充后的数据数组
  // 排序机柜数据
  const cabinet = sortCabinetsByUsStart(newItems)
  // 遍历机柜中的设备数据
  for (const item of cabinet) {
    // 填充img
    item.img = imgMap[item.resourceTypeCode]
    item.infoApi = !banList.includes(item.resourceTypeCode) // 设备是否有点击不让他去请求数据
    // 在当前设备前填充盲板
    while (currentIndex < item.usStart) {
      filledData.push({
        name: "盲板",
        img: panel, // 盲板图片路径
        desc: "盲板",
        usHeight: 1,
        usStart: currentIndex,
        id: "9999", // 盲板ID，可以根据需要生成唯一ID
        info: false,
      });
      currentIndex++; // 移动到下一个格子
    }

    // 添加当前设备到填充后的数据数组
    filledData.push({
      ...item,
      info: true, // 表示这是一个设备，不是盲板
    });

    // 跳过当前设备占据的格子数
    currentIndex += item.usHeight;
  }

  // 在最后一个设备后填充盲板，直到填满所有格子
  while (currentIndex < totalSlots) {
    filledData.push({
      name: "panel",
      img: panel, // 盲板图片路径
      desc: "盲板",
      usHeight: 1,
      usStart: currentIndex,
      id: '9999', // 盲板ID，可以根据需要生成唯一ID
      info: false,
    });
    currentIndex++;
  }

  // 返回填充后的机柜数据
  let newfilledData = [...filledData].reverse()
  return [...newfilledData];
}
// 项目不同的设备总的设备图
export const machineRoomAll = reactive({
  xiAn: {
    url: jksb1,
    cabinet: { top: "0px" },
    data: [
      { id: 1, title: "罗兰监测-1号柜", nameStyle: { top: "-38px", left: "46px" }, regionStyle: { top: "66px", left: "86px", width: "137px", height: "410px" } },
      { id: 2, title: "罗兰监测-2号柜", nameStyle: { top: "-15px", left: "195px" }, regionStyle: { top: "87px", left: "248px", width: "119px", height: "370px" } },
      { id: 3, title: "罗兰监测-3号柜", nameStyle: { top: "8px", left: "345px" }, regionStyle: { top: "104px", left: "391px", width: "104px", height: "340px" } },
      { id: 4, title: "罗兰监测-4号柜", nameStyle: { top: "35px", left: "495px" }, regionStyle: { top: "124px", left: "512px", width: "100px", height: "300px" } },
      { id: 5, title: "星地融合-1号柜", nameStyle: { top: "40px", right: "259px" }, regionStyle: { top: "125px", right: "297px", width: "96px", height: "287px" } },
      { id: 6, title: "监测运控-1号柜", nameStyle: { top: "22px", right: "43px" }, regionStyle: { top: "106px", right: "70px", width: "115px", height: "340px" } }]
  },
  dunHuang: {
    url: dunHuangImg,
    // 机柜位置坐标
    cabinet: { top: "-30px" },
    data: [
      { id: "1", title: "罗兰监测-1号柜", nameStyle: { top: "-34px", left: "88px" }, regionStyle: { top: "66px", left: "114px", width: "152px", height: "410px" } },
      { id: "2", title: "罗兰监测-2号柜", nameStyle: { top: "-15px", left: "242px" }, regionStyle: { top: "87px", left: "283px", width: "119px", height: "370px" } },
      { id: "3", title: "罗兰监测-3号柜", nameStyle: { top: "12px", left: "390px" }, regionStyle: { top: "104px", left: "412px", width: "104px", height: "340px" } },
      { id: "4", title: "罗兰监测-4号柜", nameStyle: { top: "43px", left: "521px" }, regionStyle: { top: "124px", left: "524px", width: "100px", height: "300px" } },
      { id: "5", title: "监测运控-1号柜", nameStyle: { top: "43px", right: "250px" }, regionStyle: { top: "125px", right: "283px", width: "96px", height: "313px" } },
      { id: "6", title: "监测运控-电池柜", nameStyle: { top: "15px", right: "85px" }, regionStyle: { top: "106px", right: "126px", width: "115px", height: "340px" } }]
  },
  yunKong: {
    url: jksb3
  }
})
// 排序设备数据
function sortCabinetsByUsStart(cabinets) {
  return cabinets.sort((a, b) => a.usStart - b.usStart);
}
