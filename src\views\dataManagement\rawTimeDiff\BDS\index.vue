<template>
  <div class="eop-containers">
    <navList :navList="navItems" :index="activeIndex" @setoriData="handleNavChange" class="nav-list" />
    <Klobuchar v-if="activeIndex === 1" />
    <BDGIM v-if="activeIndex === 2" />
    <GPS v-if="activeIndex === 3" />
    <Galileo v-if="activeIndex === 4" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import navList from "@/components/navList/navList.vue";
import { useRouter } from "vue-router";
import Klobuchar from "./Klobuchar.vue";
import BDGIM from "./BDGIM.vue";
import GPS from "./GPS.vue";
import Galileo from "./Galileo.vue";
const router = useRouter();
const activeIndex = ref(1);

const navItems = [
  { id: 1, name: "BDS D1/D2 Klobuchar" },
  { id: 2, name: "BDS CNAV BDGIM" },
  { id: 3, name: "GPS Klobuchar" },
  { id: 4, name: "Galileo NeQuick" }
];
// 将数字转换为科学计数法
const toScientificNotation = (numStr) => {
  const num = parseFloat(numStr);
  return num.toExponential(4);
};
// 处理导航切换
const handleNavChange = (id) => {
  activeIndex.value = id;
  // switch (id) {
  //   case 1:
  //     router.push("/dataManagement/rawTimeDiff/EOP/Klobuchar");
  //     break;
  //   case 2:
  //     router.push("/dataManagement/rawTimeDiff/EOP/BDGIM");
  //     break;
  //   case 3:
  //     router.push("/dataManagement/rawTimeDiff/EOP/GPS");
  //     break;
  //   default:
  //     router.push("/dataManagement/rawTimeDiff/EOP/Klobuchar");
  // }
};
</script>

<style scoped lang="scss">
.eop-containers {
  margin-top: 16px;
  padding: 16px;
  background: #fff;
  .nav-list {
    border-bottom: 1px solid #e5e5e5;
  }
}
</style>
