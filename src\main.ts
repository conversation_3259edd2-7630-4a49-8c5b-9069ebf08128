import { createApp } from 'vue';
import '@/styles/element/index.scss'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import ElementPlus from 'element-plus';
import App from './App.vue';
import './plugins/assets';
import {  setupDayjs, setupIconifyOffline, setupLoading, setupNProgress } from './plugins';
import { setupStore } from './store';
import { setupRouter } from './router';
import { echartsInitPlugin } from './plugins/echarts-init';
import zhCn from 'element-plus/es/locale/lang/zh-cn';
async function setupApp() {
  setupLoading();

  setupNProgress();

  setupIconifyOffline();

  setupDayjs();

  const app = createApp(App);

  setupStore(app);

  await setupRouter(app);
  for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
    app.component(key, component);
  }

  app.use(echartsInitPlugin);
  app.use(ElementPlus, {
    locale: zhCn,
  });
  app.mount('#app');
}

setupApp();
