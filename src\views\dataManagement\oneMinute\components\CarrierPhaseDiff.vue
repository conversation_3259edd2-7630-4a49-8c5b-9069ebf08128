<template>
  <div class="b2b-ppp-diff">
    <!-- 查询表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="searchForm">

        <el-form-item label="时间">
          <el-date-picker
            style="width: 300px"
            v-model="searchForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :disabled-date="configure.disabledDate"
            :shortcuts="configure.dateShortcuts"
          />
        </el-form-item>


        <el-form-item>
          <el-button type="primary" @click="onSearch" :loading="loading">查询</el-button>
          <el-button type="success" @click="viewTrend">查看趋势图</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="table-wrapper">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        height="65vh"
        v-loading="loading"
        :header-cell-style="headerCellStyle"
        :cell-style="cellStyle"
      >
        <el-table-column
          prop="timestamp"
          label="时间"
          width="180"
        />

        <el-table-column prop="bdsFrequencyComb" width="130" label="BDS频点">
          <template #default="scope">
            {{ scope.row.bdsFrequencyComb || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="bdsFrequencyBias" label="UTC(NTSC)-BDT (ns)">
          <template #default="scope">
            {{ scope.row.bdsFrequencyBias || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="gpsFrequencyComb" width="130" label="GPS频点">
          <template #default="scope">
            {{ scope.row.gpsFrequencyComb || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="gpsFrequencyBias" label="UTC(NTSC)-GPST (ns)">
          <template #default="scope">
            {{ scope.row.gpsFrequencyBias || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="glonassFrequencyComb" width="130" label="GLONASS频点">
          <template #default="scope">
            {{ scope.row.glonassFrequencyComb || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="glonassFrequencyBias" label="UTC(NTSC)-GLONASST (ns)">
          <template #default="scope">
            {{ scope.row.glonassFrequencyBias || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="galileoFrequencyComb" width="130" label="Galileo频点">
          <template #default="scope">
            {{ scope.row.galileoFrequencyComb || '--' }}
          </template>
        </el-table-column>
        <el-table-column prop="galileoFrequencyBias" label="UTC(NTSC)-GST (ns)">
          <template #default="scope">
            {{ scope.row.galileoFrequencyBias || '--' }}
          </template>
        </el-table-column>
      </el-table>
      <!-- 分页器 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 30, 50]"
          layout="sizes, total, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 趋势图弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="数据趋势图"
      width="80%"
      :before-close="() => (dialogVisible = false)"
    >
      <div class="chart-content">
        <line-chart :data="lineChartOption" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from "vue";
import configure from "@/utils/configure.js";
import apiAjax from "@/api/index";

// 表格数据
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);

// 搜索表单
const searchForm = reactive({
  timeRange: [],
});

// 分页配置
const currentPage = ref(1);
const pageSize = ref(10);

// 表头样式
const headerCellStyle = {
  background: "#f5f7fa",
  color: "#606266",
  fontWeight: "bold",
  fontSize: "18px",
  height: "50px",
};

// 单元格样式
const cellStyle = {
  fontSize: "16px",
  padding: "10px 0",
};

// 构建查询参数
const buildQueryParams = (page, size) => {
  const params = new URLSearchParams();

  // 时间范围参数
  if (searchForm.timeRange?.length === 2) {
    params.append(
      "beginTime",
      configure.formatDate(new Date(`${searchForm.timeRange[0]} 00:00:00`)),
    );
    params.append(
      "endTime",
      configure.formatDate(new Date(`${searchForm.timeRange[1]} 23:59:59`)),
    );
  }
  if (searchForm.dataSource) {
    params.append("dataSource", searchForm.dataSource);
  }

  // 分页参数
  params.append("page", page);
  params.append("size", size);

  return params.toString();
};

// 加载数据
const loadData = async () => {
  loading.value = true;
  try {
    const queryString = buildQueryParams(
      currentPage.value,
      pageSize.value,
    );
    const { data: response } = await apiAjax.get(
      `/api/jnx/fusion/apps/satellite/fusion/oneMinute/findXtzbxwscPage?${queryString}`,
    );

    if (response) {
      tableData.value = response.content || [];
      total.value = response.totalElements || 0;
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理函数
const onSearch = () => {
  currentPage.value = 1;
  loadData();
};

// 分页处理函数
const handleSizeChange = (val) => {
  currentPage.value = 1;
  pageSize.value = val;
  loadData();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadData();
};

// 弹窗相关
const dialogVisible = ref(false);

// 图表配置
const lineChartOption = ref({
  xAxis: {
    type: "category",
    data: Array.from(
      { length: 24 },
      (_, i) => `${String(i).padStart(2, "0")}:00`,
    ),
  },
  yAxis: {
    type: "value",
    name: "时差(ns)",
  },
  legend: {
    bottom: 10,
    data: [
      "UTC(NTSC)-BDT",
      "UTC(NTSC)-GPST",
      "UTC(NTSC)-GLNT",
      "UTC(NTSC)-GST",
    ],
  },
  grid: {
    top: "30px",
    left: "50px",
    right: "30px",
    bottom: "60px",
  },
  tooltip: {
    trigger: "axis",
  },
  series: [
    {
      name: "UTC(NTSC)-BDT",
      data: Array.from({ length: 24 }, () =>
        (Math.random() * 200 - 100).toFixed(1),
      ),
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: { color: "#3B8CFF" },
      lineStyle: { width: 2 },
    },
    {
      name: "UTC(NTSC)-GPST",
      data: Array.from({ length: 24 }, () =>
        (Math.random() * 200 - 100).toFixed(1),
      ),
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: { color: "#FFA07A" },
      lineStyle: { width: 2 },
    },
    {
      name: "UTC(NTSC)-GLNT",
      data: Array.from({ length: 24 }, () =>
        (Math.random() * 200 - 100).toFixed(1),
      ),
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: { color: "#4CAF50" },
      lineStyle: { width: 2 },
    },
    {
      name: "UTC(NTSC)-GST",
      data: Array.from({ length: 24 }, () =>
        (Math.random() * 200 - 100).toFixed(1),
      ),
      type: "line",
      smooth: true,
      showSymbol: true,
      symbolSize: 6,
      itemStyle: { color: "#FF6347" },
      lineStyle: { width: 2 },
    },
  ],
});

// 查看趋势图
const viewTrend = () => {
  dialogVisible.value = true;
};

// 初始加载数据
onMounted(() => {
  loadData();
});
</script>

<style lang="scss" scoped>
.b2b-ppp-diff {
  .search-form {
    background-color: #fff;
    padding: 12px;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .chart-content {
    height: 400px;
    margin-bottom: 20px;
  }

  .table-wrapper {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

    .table-title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 15px;
      color: #303133;
      padding-left: 5px;
      border-left: 4px solid #409eff;
    }

    .pagination-wrapper {
      margin-top: 16px;
      padding: 10px;
      display: flex;
      justify-content: flex-end;
      background-color: #fff;
    }
  }
}
</style>
