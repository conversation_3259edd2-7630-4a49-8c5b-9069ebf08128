<template>
  <div class="transceiver-container">
    <!-- <h3>收发数据</h3> -->

    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="开始时间">
          <el-date-picker
            v-model="searchForm.startTime"
            type="datetime"
            placeholder="选择开始时间"
          />
        </el-form-item>
        <el-form-item label="结束时间">
          <el-date-picker
            v-model="searchForm.endTime"
            type="datetime"
            placeholder="选择结束时间"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <el-table :data="tableData" class="table-border" border style="width: 100%">
      <el-table-column type="index" label="序号" width="80" />
      <el-table-column prop="sendTime" label="发送时间" width="180" />
      <el-table-column prop="dataType" label="数据类型" />
      <el-table-column prop="receiver" label="接受对象" />
      <el-table-column prop="source" label="数原始" width="100" />
      <el-table-column label="发送状态" width="100">
        <template #default="{ row }">
          <el-tag :type="row.status === '成功' ? 'success' : 'danger'">
            {{ row.status }}
          </el-tag>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[10, 20, 30, 50]"
        layout="total, sizes, prev, pager, next"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'

// 搜索表单
const searchForm = reactive({
  startTime: '',
  endTime: ''
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 表格数据
const tableData = ref([
  {
    dataType: '实时数据',
    receiver: 'xxxxx系统',
    source: '234',
    sendTime: '2024-11-20 13:23:21',
    status: '成功'
  },
  {
    dataType: '历史数据',
    receiver: 'yyyyy系统',
    source: '567',
    sendTime: '2024-11-21 14:34:45',
    status: '失败'
  },
  {
    dataType: '实时数据',
    receiver: 'zzzzz系统',
    source: '890',
    sendTime: '2024-11-22 15:45:56',
    status: '成功'
  },
  {
    dataType: '历史数据',
    receiver: 'aaaaa系统',
    source: '101',
    sendTime: '2024-11-23 16:56:07',
    status: '失败'
  },
  {
    dataType: '实时数据',
    receiver: 'bbbbb系统',
    source: '112',
    sendTime: '2024-11-24 17:67:08',
    status: '成功'
  }
  // ... 更多数据
])

// 搜索方法
const handleSearch = () => {
  // 实现搜索逻辑
}

// 导出方法
const handleExport = () => {
  // 实现导出逻辑
}

// 分页方法
const handleSizeChange = (val: number) => {
  pageSize.value = val
  // 重新获取数据
}

const handleCurrentChange = (val: number) => {
  currentPage.value = val
  // 重新获取数据
}
</script>

<style scoped lang="scss">
.transceiver-container {
  margin-top: 16px;
  padding: 16px;
  background: #fff;
  .search-area {
    margin-bottom: 16px;
  }

  .pagination-container {
    margin-top: 16px;
    display: flex;
    justify-content: flex-end;
  }
}
@media only screen and (max-height: 1080px) {
  .table-border{
    height: 830px;
  }
}
@media only screen and (max-height: 919px) {
  .table-border{
    height: 680px;
  }
}
</style>
