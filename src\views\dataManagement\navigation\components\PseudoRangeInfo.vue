<template>
  <div class="pseudo-range-info">
    <div class="search-form">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="导航系统">
          <el-select
            style="width: 180px"
            v-model="searchForm.navigationSystemCode"
            placeholder="请选择系统"
            @change="handleSystemChange"
          >
            <el-option
              v-for="item in systemOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="卫星PRN">
          <el-select
            style="width: 180px"
            v-model="searchForm.prn"
            placeholder="请选择PRN"
          >
            <el-option label="全部" value="" />
            <el-option
              v-for="item in prnOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker
            style="width: 300px"
            v-model="searchForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :disabled-date="configure.disabledDate"
            :shortcuts="configure.dateShortcuts"
          />
        </el-form-item>
        <el-form-item label="数据来源">
          <el-select
            style="width: 160px"
            v-model="searchForm.dataSource"
            placeholder="请选择数据来源"
          >
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch" :loading="loading">查询</el-button>
          <el-button type="success" @click="viewTrend">查看趋势图</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="tables-section">
      <div class="table-wrapper">
        <!-- <div class="table-title">卫星伪码授时偏差</div> -->
        <el-table
          :data="tableData"
          border
          style="width: 1650px"
          height="58vh"
          v-loading="loading"
          :header-cell-style="headerCellStyle"
          :cell-style="cellStyle"
        >
          <el-table-column prop="time" label="UTC-时间" width="180" />
          <el-table-column prop="resourceTypeName" label="数据来源" width="280">
            <template #default="scope">
              {{
                scope.row.resourceInstanceName +
                `(${scope.row.resourceInstanceCode})`
              }}
            </template>
          </el-table-column>
          <el-table-column prop="satelliteId" label="卫星号" width="100" />
          <el-table-column
            v-for="(header, index) in dynamicHeaders"
            :key="index"
            :prop="header.prop"
            :label="header.label"
          />
        </el-table>
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="sizes, total, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 趋势图弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      title="数据趋势图"
      width="70%"
      :before-close="() => (dialogVisible = false)"
    >
      <div class="chart-container">
        <div class="chart-header">
          <h3>卫星伪码授时偏差趋势</h3>
          <div class="system-select">
            <span>频点：</span>
            <el-select
              v-model="selectedSystem"
              placeholder="请选择系统"
              clearable
              style="width: 120px"
            >
              <el-option label="B1I" value="B1I"></el-option>
              <el-option label="B3I" value="B3I"></el-option>
              <el-option label="B1C" value="B1C"></el-option>
              <el-option label="B2a" value="B2a"></el-option>
            </el-select>
          </div>
        </div>
        <div class="chart-content">
          <line-chart :data="lineChartOption" />
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive } from "vue";
import configure from "@/utils/configure.js";
import apiAjax from "@/api/index";
import { Search } from "@element-plus/icons-vue";
import { useAllSatellite } from "@/store/modules/allSatellite";
const allSatelliteStore = useAllSatellite();

// 表头样式
const headerCellStyle = {
  background: "#f5f7fa",
  color: "#606266",
  fontWeight: "bold",
  fontSize: "18px",
  height: "50px",
};

// 单元格样式
const cellStyle = {
  fontSize: "16px",
  padding: "10px 0",
};

// 弹窗相关
const dialogVisible = ref(false);

// 生成卫星编号列表
const generateSatelliteIds = () => {
  const satellites = [];
  // BDS: C01-C63
  for (let i = 1; i <= 63; i++) {
    satellites.push(`C${String(i).padStart(2, "0")}`);
  }
  // GPS: G01-G32
  for (let i = 1; i <= 32; i++) {
    satellites.push(`G${String(i).padStart(2, "0")}`);
  }
  // GLONASS: R01-R27
  for (let i = 1; i <= 27; i++) {
    satellites.push(`R${String(i).padStart(2, "0")}`);
  }
  // GALILEO: E01-E30
  for (let i = 1; i <= 30; i++) {
    satellites.push(`E${String(i).padStart(2, "0")}`);
  }
  return satellites;
};

const satellites = generateSatelliteIds();

// 修改图表配置
const lineChartOption = ref({
  xAxis: {
    type: "category",
    data: Array.from(
      { length: 24 },
      (_, i) => `${String(i).padStart(2, "0")}:00`,
    ),
  },
  yAxis: {
    type: "value",
    name: "时差(ns)",
  },
  legend: {
    type: "scroll",
    bottom: 10,
    data: satellites.slice(0, 10),
  },
  grid: {
    top: "30px",
    left: "50px",
    right: "30px",
    bottom: "60px",
  },
  tooltip: {
    trigger: "axis",
    formatter: function (params) {
      // 获取当前日期
      const now = new Date();
      // 提取小时数
      const hour = parseInt(params[0].name.split(":")[0]);
      // 创建完整的UTC时间
      const date = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate(),
        hour,
        0,
        0,
      );
      const fullTime = formatUTCTime(date);

      let result = `UTC时间: ${fullTime}<br/>`;
      params.forEach((param) => {
        result += `${param.seriesName}: ${param.value} ns<br/>`;
      });
      return result;
    },
  },
  series: satellites.slice(0, 10).map((satellite) => ({
    name: satellite,
    data: Array.from({ length: 24 }, () =>
      (Math.random() * 200 - 100).toFixed(1),
    ),
    type: "line",
    showSymbol: true,
    symbolSize: 6,
    smooth: true,
    lineStyle: { width: 2 },
  })),
});

// 修改时间格式化函数
const formatUTCTime = (date) => {
  return date.toISOString().replace("T", " ").slice(0, -5);
};

// 修改数据生成函数
const timeDiffList = ref([]);
const total = ref(0);

const typeOptions = [
  { label: "全部", value: "" },
  { label: "SAM1", value: "SAM1" },
  { label: "SAM2", value: "SAM2" },
  { label: "SAM3", value: "SAM3" },
];

const loading = ref(false);

const loadData = async () => {
  loading.value = true;
  try {
    const params = new URLSearchParams();
    // 添加查询参数
    if (searchForm.navigationSystemCode) {
      params.append("navigationSystemCode", searchForm.navigationSystemCode);
    }
    if (searchForm.prn) {
      params.append("satellitePrn", searchForm.prn);
    }
    if (searchForm.timeRange?.length === 2) {
      params.append(
        "beginTime",
        configure.formatDate(new Date(`${searchForm.timeRange[0]} 00:00:00`)),
      );
      params.append(
        "endTime",
        configure.formatDate(new Date(`${searchForm.timeRange[1]} 23:59:59`)),
      );
    }
    // 分页参数
    params.append("page", currentPage.value);
    params.append("size", pageSize.value);
    if (searchForm.dataSource) {
      params.append("dataSource", searchForm.dataSource);
    }
    const { data: response } = await apiAjax.get(
      `/api/jnx/fusion/apps/satellite/collect/timeDiff/findWxwmsspcPage?${params.toString()}`,
    );

    if (response) {
      timeDiffList.value = response.content || [];
      total.value = response.totalElements || 0;
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  } finally {
    loading.value = false;
  }
};

const searchForm = reactive({
  navigationSystemCode: "",
  prn: "",
  timeRange: [],
  dataSource: "",
});

// 导航系统选项
const systemOptions = computed(() => {
  if (!allSatelliteStore.data || !allSatelliteStore.data.length) return [];
  return allSatelliteStore.data.map((system) => ({
    value: system.systemCode,
    label: system.systemName,
  }));
});

// 卫星PRN码选项
const prnOptions = computed(() => {
  if (!searchForm.navigationSystemCode || !allSatelliteStore.data) return [];

  // 找到选中的导航系统
  const selectedSystem = allSatelliteStore.data.find(
    (system) => system.systemCode === searchForm.navigationSystemCode,
  );

  if (!selectedSystem || !selectedSystem.satelliteInfos) return [];

  // 返回该系统下的所有卫星PRN
  return selectedSystem.satelliteInfos.map((satellite) => ({
    value: satellite.prn,
    label: satellite.prn,
  }));
});

// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  loadData();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadData();
};

// 查看趋势图
const viewTrend = () => {
  dialogVisible.value = true;
};

// 获取频点列表
const getFrequencyList = () => {
  switch (searchForm.navigationSystemCode) {
    case "01": // BDS
      return ["B1I", "B3I", "B1C", "B2a", "B1IB3I", "B1CB2a"];
    case "02": // GPS
      return ["L1", "L2", "L5", "L1L2", "L1L5"];
    case "03": // GLONASS
      return ["G1", "G2", "G1G2"];
    case "04": // Galileo
      return ["E1", "E5a", "E5b", "E1E5a", "E1E5b"];
    default:
      return ["频点1", "频点2", "频点3", "频点4", "频点5", "频点6"];
  }
};

// 动态表头
const dynamicHeaders = computed(() => {
  const frequencyList = getFrequencyList();

  // 根据所选导航系统返回对应的频点表头
  return frequencyList.map((freq, index) => ({
    label: freq+"(ns)",
    prop: `frequencyBias_${index}`,
  }));
});

// 表格数据
const tableData = computed(() => {
  if (!timeDiffList.value || timeDiffList.value.length === 0) return [];

  return timeDiffList.value.map((item) => {
    const result = {
      time: item.timestamp,
      satelliteId: item.satellitePrn,
      resourceInstanceName: item.resourceInstanceName,
      resourceTypeCode: item.resourceTypeCode,
      resourceInstanceCode: item.resourceInstanceCode,
    };

    // 将frequencyBias数组映射到表格列
    if (item.frequencyBias && Array.isArray(item.frequencyBias)) {
      item.frequencyBias.forEach((value, index) => {
        // result[`frequencyBias_${index}`] = value === 999.99 ? "无效" : value;
        result[`frequencyBias_${index}`] = value;
      });
    }

    return result;
  });
});

// 处理导航系统变更
const handleSystemChange = () => {
  // 清空PRN选择
  searchForm.prn = "";
  // // 重新加载数据
  currentPage.value = 1;
  loadData();
};

// 搜索处理函数
const onSearch = () => {
  currentPage.value = 1;
  loadData();
};

onMounted(async () => {
  await allSatelliteStore.initAllData();
  searchForm.navigationSystemCode = allSatelliteStore.data[0].systemCode;
  loadData();
});
</script>

<style lang="scss" scoped>
.pseudo-range-info {
  height: 100%;
  .search-form {
    background-color: #fff;
    padding: 12px;
    border-radius: 4px;
    // margin-bottom: 16px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .tables-section {
    display: flex;
    gap: 20px;
    // margin-top: 20px;

    .table-wrapper {
      flex: 1;
      background-color: #fff;
      border-radius: 4px;
      // padding: 15px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .table-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #303133;
        padding-left: 5px;
        border-left: 4px solid #409eff;
      }

      .pagination-wrapper {
        margin-top: 16px;
        padding: 10px;
        display: flex;
        justify-content: flex-end;
        background-color: #fff;
      }
    }
  }
}
.chart-container {
  width: 100%;

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;

    h3 {
      margin: 0;
      font-size: 16px;
      color: #303133;
    }

    .system-select {
      display: flex;
      align-items: center;

      span {
        margin-right: 8px;
        font-size: 14px;
      }
    }
  }

  .chart-content {
    width: 100%;
    height: 500px;
  }
}
</style>
