import { SetupStoreId } from '@/enum';
import { defineStore } from 'pinia';
import { reactive, toRefs } from 'vue';

export const #useStoreName# = defineStore(
  SetupStoreId.#bigStoreName#, // 创建自己的store前要添加一个枚举名称，该名称必须全局唯一
  () => {
    // states
    const data = reactive({});

    // getters

    // actions

    return {
      ...toRefs(data),
    };
  },
  {
    // 是否持久化到浏览器缓存localStorage
    persist: false,
  }
);
