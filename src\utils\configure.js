import axios from 'axios';
import { localStg } from '@/utils/storage';
let typeObj = {
  "6780M": '6780M 南海-贺州',
  "6780X": '6780X 南海-饶平',
  "6780Y": '6780Y 南海-崇左',
  "7430M": '7430M 北海-荣成',
  "7430X": '7430X 北海-宣城',
  "7430Y": '7430Y 北海-和龙',
  "8390M": '8390M 东海-宣城',
  "8390X": '8390X 东海-饶平',
  "8390Y": '8390Y 东海-荣成',
  "6000M": '6000M 蒲城',
  "8750M": '8750M 敦煌',
  "7600M": "7600M 库尔勒",
  "6250M": "6250M 那曲"
}
const thoroughfare = (type) => {
  if (!typeObj[type]) {
    return "未知类型"
  } else {
    return typeObj[type]
  }
}
const stationAll = {
  "J01": "西安站",
  "J02": "敦煌站",
  "J03": "库尔勒站",
  "J04": "拉萨站",
  "J05": "西安中心",
}

const stationAllLoginLongs = {
  "J01": "西安授时监测站",
  "J02": "敦煌授时监测站",
  "J03": "库尔勒授时监测站",
  "J04": "拉萨授时监测站",
  "J05": "西安监测中心站",
}
const stationAllLoginLong = stationAllLoginLongs[import.meta.env.VITE_SERVICE_BASE_TYPECODE]




function isEmpty(value) {
  // 判断是否为对象或数组
  if (value === null || typeof value !== 'object') {
    return true
  }

  // 如果是数组或对象，进行空判断
  if (Array.isArray(value)) {
    // 如果是数组，检查其长度
    return value.length === 0;
  } else {
    // 如果是对象，检查其属性个数
    return Object.keys(value).length === 0;
  }
}
//格式化时间的
const setDate = (timeString, info) => {
  const year = parseInt(timeString.substring(0, 4), 10);
  const month = parseInt(timeString.substring(4, 6), 10) - 1; // 月份从0开始
  const day = parseInt(timeString.substring(6, 8), 10);
  const hours = parseInt(timeString.substring(8, 10), 10);
  const minutes = parseInt(timeString.substring(10, 12), 10);
  const seconds = parseInt(timeString.substring(12, 14), 10);
  // 创建 Date 对象
  const date = new Date(year, month, day, hours, minutes, seconds);
  // 获取时间戳（毫秒）
  const timestampMs = date.getTime();
  return timestampMs;
};

// 得到当前utc 时间或者是更具时间搓来计算utc 时间
const getUtcDate = (date = false) => {
  let utcDate = date ? new Date(date) : new Date();
  let utc = utcDate.toISOString().slice(0, -5) + utcDate.toISOString().slice(23, 24);
  return utc;
}
//当前得到N分钟前的时间戳
const getTimestampTwoMinutesAgo = (n = 2) => {
  // 获取当前时间戳
  const now = Date.now();

  // 计算N分钟的毫秒数
  const twoMinutesInMillis = n * 60 * 1000;

  // 计算两分钟之前的时间戳
  return getUtcDate(now - twoMinutesInMillis);
}

//去重函数
const duplicate = (list, key) => {
  return list.reduce((accumulator, current) => {
    const existing = accumulator.find(item => item[key] === current[key]);
    if (!existing) {
      accumulator.push(current);
    }
    return accumulator;
  }, []);
}

// 更具字段长度 返回 对应的px
const setPx = (value) => {
  return value.length * 7
}

// const download = async (newUrl, type = false) => {

//   try {
//     const token = localStg.get("token") || false;
//     // 发送带有验证头的请求
//     const response = await axios({
//       url: "http://10.17.7.36:20001/api/jnx/fusion/apps/satellite/fileInfo/downloadFile?id=" + newUrl,
//       method: 'GET',
//       responseType: 'blob', // 关键设置，表示响应类型为 blob
//       headers: {
//         'Authorization': `${token}` // 这里的 token 是你的验证头
//       }
//     });
//     console.log(response);
//     // 获取文件的 MIME 类型
//     const contentType = response.headers['content-type'];
//     const contentDisposition = response.headers['content-disposition'];
//     const filenameMatch = contentDisposition && contentDisposition.match(/filename="(.+)"/);
//     const filename = Date.now() + type;

//     // 创建一个 a 标签用于下载文件
//     const url = window.URL.createObjectURL(new Blob([response.data]));
//     const link = document.createElement('a');
//     link.href = url;
//     link.setAttribute('download', type ? filename : '1111');
//     document.body.appendChild(link);
//     link.click();

//     // 清理
//     document.body.removeChild(link);
//     window.URL.revokeObjectURL(url);
//   } catch (error) {
//     console.error('下载文件时出错:', error);
//   }
// };


const download = async (newUrl, type = false) => {
  const token = localStg.get("token") || false;
  var xhr = new XMLHttpRequest();//new iframeDoc.XMLHttpRequest();
  xhr.responseType = 'blob'
  xhr.open('GET', "http://10.17.99.36:20001/api/jnx/fusion/apps/satellite/fileInfo/downloadFile?id=" + newUrl, true);
  xhr.setRequestHeader('Authorization', token);
  xhr.onload = function () {
    const disposition = xhr.getResponseHeader('content-disposition');
    const url = window.URL.createObjectURL(xhr.response) //将后端返回的blob文件读取出url
    let name = disposition.split("''")?.[1] || "1";
    const link = document.createElement("a");
    link.href = url;
    link.download = name;
    link.target = "_blank";
    link.click();
  }
  xhr.send();
}

function numberToChinese(newnum) {
  const units = ["", "十", "百", "千", "万", "亿", "兆"];
  const digits = ["零", "一", "二", "三", "四", "五", "六", "七", "八", "九"];
  var num = newnum - 0;
  if (num === 0) return digits[0]; // 处理零

  let result = '';

  let unitPos = 0; // 当前单位位置
  let zeroFlag = false; // 是否需要加零

  // 从后往前处理数字
  while (num > 0) {
    const digit = num % 10; // 取出当前位的数字
    if (digit > 0) {
      result = digits[digit] + units[unitPos] + result; // 添加数字和单位
      zeroFlag = false; // 不需要加零
    } else if (!zeroFlag) {
      result = digits[0] + result; // 需要加零
      zeroFlag = true; // 下一次再遇到零时不再添加
    }

    num = Math.floor(num / 10); // 去掉当前位
    unitPos++; // 移动到下一位
  }

  // 处理万和亿之间的零，如 10101 应为“一万零一百零一”
  result = result.replace(/零+/g, "零"); // 替换多个零为一个
  result = result.replace(/零+$/, ""); // 去掉末尾的零

  return result;
}
// 默认选中前一天的状态
const setDefaultDates = (n = 1) => {
  const now = new Date();
  const yesterday = new Date();
  yesterday.setDate(now.getDate() - n); // 设置为前一天
  return [yesterday, now]; // 返回前一天到现在的时间范围
}

// 格式化时间  // 传入的是时间搓
function formatTimestamp(timestamp) {
  // 创建一个 Date 对象
  const date = new Date(timestamp);

  // 获取年、月、日、时、分、秒
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  // 拼接成指定格式的字符串
  const formatted = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;

  return formatted;
}

const dateShortcuts = [
  {
    text: "最近一周",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    },
  },
  {
    text: "最近一个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    },
  },
  {
    text: "最近三个月",
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    },
  },
];

// 禁用日期 - 禁用未来日期
const disabledDate = (time) => {
  return time.getTime() > Date.now();
};
// 格式化日期为yyyy-MM-dd HH:mm:ss
const formatDate = (date, withTime = true) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return withTime
    ? `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    : `${year}-${month}-${day}`;
};

export default {
  thoroughfare,
  isEmpty,
  setDate,
  getUtcDate,
  getTimestampTwoMinutesAgo,
  duplicate,
  setPx,
  download,
  numberToChinese,
  setDefaultDates,
  typeObj,
  formatTimestamp,
  dateShortcuts,
  disabledDate,
  formatDate,
  stationAllLoginLong
}

