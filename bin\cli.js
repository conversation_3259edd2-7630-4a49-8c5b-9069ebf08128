import { program } from 'commander';
import fs from 'fs';
import path from 'path';
import fse from 'fs-extra';

const getAbsolutePath = (filePath) => {
  const absolutePath = path.join(process.cwd(), filePath); // 转换为绝对路径
  return absolutePath;
}

const viewsPath = getAbsolutePath('./src/views');
const viewsTemplatePath = getAbsolutePath('./bin/template/view.vue');
const storesPath = getAbsolutePath('./src/store/modules');
const storesTemplatePath = getAbsolutePath('./bin/template/store.ts');
const storeEnumPath = getAbsolutePath('./src/enum/index.ts');

const mkdir = (folderName) => {
  if(isDirectoryExists(folderName)) {
    console.error(`文件夹：${folderName}已存在，需删除后再创建模块。`);
    return false
  } else {
    try {
      fs.mkdirSync(folderName, { recursive: true });
      console.log(`成功创建文件夹: ${folderName}`);
      return true;
    } catch(err) {
      return false;
    }
  }
}

const isDirectoryExists = (path) => {
  try {
    fs.accessSync(path, fs.constants.R_OK);
    return fs.lstatSync(path).isDirectory();
  } catch (err) {
    return false;
  }
}

const isDirectoryEmpty = (directoryPath) => {
  try {
    const files = fs.readdirSync(directoryPath);
    return files.length === 0;
  } catch (err) {
    console.error(err);
    return false;
  }
}

const createFile = (filePath, content='', isUpdate=false) => {
  try {
    fs.writeFileSync(filePath, content);
    console.log(`成功${isUpdate ? '更新' : '创建'}文件：${filePath}!`);
    return true;
  } catch (err) {
    console.error(`${isUpdate ? '更新' : '创建'}文件失败: ${err}`);
    return false;
  }
}

const readFile = (filePath, code='utf8') => {
  try {
    const data = fs.readFileSync(filePath, code);
    return data;
  } catch (err) {
    console.error(err);
    return false;
  }
}

const getHump = (name, isFirstUpper=false) => {
  const arr = name.split('-');
  const result = arr.map((item, index) => {
    if(!isFirstUpper && index === 0) {
      return item;
    }
    const letters = item.split('');
    letters[0] = letters[0].toLocaleUpperCase();
    return letters.join('');
  });
  return result.join('')
}

const getStoreName = (name) => {
  return getHump(name);
}

const getBigStoreName = (name) => {
  return getHump(name, true);
}

const getUseStoreName = (name) => {
  return `use${getHump(name, true)}`;
}

const removeAll = (path, parentFolder) => {
  try {
    fse.removeSync(path);
    console.log(`成功移除目录及其包含的所有内容：${path}`);
    // 若删除的是二级目录，则判断其所在一级目录下是否还有文件，若没有则连带删除该一级目录
    if(parentFolder && isDirectoryEmpty(parentFolder)) {
      fse.removeSync(parentFolder);
      console.log(`成功移除目录及其包含的所有内容：${parentFolder}`);
    }
    return true;
  } catch (err) {
    console.error(err);
    return false;
  }
}

const addModule = (name, parentName) => {
  if(!name) {
    console.error(`需要输入模块名称。`);
    return;
  }

  // 创建views相关目录和文件
  let moduleViewFolder = '';
  let viewFile = '';

  if(!parentName) {
    // 添加一级模块
    moduleViewFolder = `${viewsPath}/${name}`;
  } else {
    // 添加二级模块
    moduleViewFolder = `${viewsPath}/${parentName}/${name}`;
  }
  viewFile = `${moduleViewFolder}/index.vue`;

  if(!mkdir(moduleViewFolder)) {
    return;
  }
  let viewContent = readFile(viewsTemplatePath);
  viewContent = viewContent.replaceAll('#name#', name);
  viewContent = viewContent.replaceAll('#storeName#', parentName ? `${parentName}/${name}` : name);
  const useStoreName = getUseStoreName(name);
  viewContent = viewContent.replaceAll('#useStoreName#', useStoreName);
  if(!createFile(viewFile, viewContent)) {
    return;
  }

  // 创建store相关目录和文件
  let moduleStoreFolder = '';
  let storeFile = '';

  if(!parentName) {
    // 添加一级模块
    moduleStoreFolder = `${storesPath}/${name}`;
  } else {
    // 添加二级模块
    moduleStoreFolder = `${storesPath}/${parentName}/${name}`;
  }
  storeFile = `${moduleStoreFolder}/index.ts`;

  if(!mkdir(moduleStoreFolder)) {
    return;
  }
  let storeContent = readFile(storesTemplatePath);
  if(storeContent === false) {
    return;
  }
  storeContent = storeContent.replaceAll('#bigStoreName#', getBigStoreName(name));
  storeContent = storeContent.replaceAll('#useStoreName#', getUseStoreName(name));
  if(!createFile(storeFile, storeContent)) {
    return;
  }
  // 添加store enum相关内容
  let storeEnumContent = readFile(storeEnumPath);
  storeEnumContent = storeEnumContent.replace(
    /(?<={)(.*?)(?=})/s, 
    `$1  ${getBigStoreName(name)} = \'${getStoreName(name)}\',\n`
  );
  if(!createFile(storeEnumPath, storeEnumContent, true)) {
    return
  }
}

const removeModule = (name, parentName) => {
  if(!name) {
    console.error(`需要输入模块名称。`);
    return;
  }

  // 删除views相关目录和文件
  let moduleViewFolder = '';
  let moduleViewParentFolder = '';

  if(!parentName) {
    // 删除一级模块
    moduleViewFolder = `${viewsPath}/${name}`;
  } else {
    // 删除二级模块
    moduleViewFolder = `${viewsPath}/${parentName}/${name}`;
    moduleViewParentFolder = `${viewsPath}/${parentName}`;
  }

  if(!removeAll(moduleViewFolder, moduleViewParentFolder)) {
    return;
  }

  // 删除store相关目录和文件
  let moduleStoreFolder = '';
  let moduleStoreParentFolder = '';

  if(!parentName) {
    // 删除一级模块
    moduleStoreFolder = `${storesPath}/${name}`;
  } else {
    // 删除二级模块
    moduleStoreFolder = `${storesPath}/${parentName}/${name}`;
    moduleStoreParentFolder = `${storesPath}/${parentName}`;
  }

  if(!removeAll(moduleStoreFolder, moduleStoreParentFolder)) {
    return;
  }
  // 从store enum移除相关内容
  let storeEnumContent = readFile(storeEnumPath);
  if(storeEnumContent === false) {
    return;
  }
  storeEnumContent = storeEnumContent.replace(
    `  ${getBigStoreName(name)} = \'${getStoreName(name)}\',\n`, '');
  if(!createFile(storeEnumPath, storeEnumContent, true)) {
    return;
  }
}
 
program
  .option('-p | --parent <name>','add|remove module parent folder name!')
  .command('addModule <parameter>')
  .description('这是一个自定义命令')
  .action((parameter) => {
    const pModuleName = program.getOptionValue('parent');
    console.log(`您执行了自定义命令addModule，模块名称是: ${parameter}，父级模块是：${pModuleName}`);
    addModule(parameter, pModuleName);
  });

program
  .command('removeModule <parameter>')
  .description('这是一个自定义命令')
  .action((parameter) => {
    const pModuleName = program.getOptionValue('parent');
    console.log(`您执行了自定义命令removeModule，模块名称是: ${parameter}，父级模块是：${pModuleName}`);
    removeModule(parameter, pModuleName);
  });
 
program.parse(process.argv);