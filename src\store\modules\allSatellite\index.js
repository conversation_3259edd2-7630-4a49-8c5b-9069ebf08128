import { SetupStoreId } from '@/enum';
import { defineStore } from 'pinia';
import { reactive, toRefs } from 'vue';
import apiAjax from "@/api/index";
export const useAllSatellite = defineStore(
  SetupStoreId.AllSatellite,
  () => {
    // states
    const allData = reactive({ data: [], info: false });
    const initAllData = async () => {
      if (!allData.info || allData.data.length == 0) {
        let { data } = await apiAjax.get("/api/jnx/fusion/apps/satellite/common/getAllSatelliteNavigation");
        if (data) {
          allData.data = data;
          allData.info = true
        }
      }
      return allData.data;
    }
    return {
      ...toRefs(allData),
      initAllData
    };
  },
  {
    // 是否持久化到浏览器缓存localStorage
    persist: false,
  }
);
