<template>
  <div class="pseudo-range-info">
    <div class="search-form">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="导航系统">
          <el-select
            style="width: 180px"
            v-model="searchForm.navigationSystemCode"
            placeholder="请选择系统"
            @change="handleSystemChange"
          >
            <el-option
              v-for="item in systemOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker
            style="width: 300px"
            v-model="searchForm.timeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            :disabled-date="configure.disabledDate"
            :shortcuts="configure.dateShortcuts"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="onSearch" :loading="loading"
            >查询</el-button
          >
        </el-form-item>
      </el-form>
    </div>

    <div class="tables-section">
      <div class="table-wrapper">
        <el-table
          :data="tableData"
          border
          style="width: 1650px"
          height="58vh"
          v-loading="loading"
          :header-cell-style="headerCellStyle"
          :cell-style="cellStyle"
        >
        <el-table-column prop="timestamp" label="时间段" width="350">
            <template #default="scope">
              {{ scope.row.timestamp + "-" + scope.row.generateTime }}
            </template>
          </el-table-column>
          <el-table-column prop="navigationSystemCode" label="导航系统">
            <template #default="scope">
              {{ getSystemOptionsName(scope.row.navigationSystemCode) }}
            </template>
          </el-table-column>
          <el-table-column prop="frequencyId" label="卫星频率" />
          <el-table-column prop="accuracyLevel" label="精度等级" />
          <el-table-column prop="std" label="STD标准差(ns)" />
        </el-table>
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="sizes, total, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, reactive } from "vue";
import configure from "@/utils/configure.js";
import apiAjax from "@/api/index";
import { Search } from "@element-plus/icons-vue";
import { useAllSatellite } from "@/store/modules/allSatellite";
const allSatelliteStore = useAllSatellite();

// 表头样式
const headerCellStyle = {
  background: "#f5f7fa",
  color: "#606266",
  fontWeight: "bold",
  fontSize: "18px",
  height: "50px",
};

// 单元格样式
const cellStyle = {
  fontSize: "16px",
  padding: "10px 0",
};
const getSystemOptionsName = (value) => {
  let item = systemOptions.value.find((i) => i.value == value);
  return item.label;
};

const tableData = ref([]);
const total = ref(0);

const loading = ref(false);

const loadData = async () => {
  loading.value = true;
  try {
    const params = new URLSearchParams();
    // 添加查询参数
    if (searchForm.navigationSystemCode) {
      params.append("navigationSystemCode", searchForm.navigationSystemCode);
    }

    if (searchForm.timeRange?.length === 2) {
      params.append(
        "beginTime",
        configure.formatDate(new Date(`${searchForm.timeRange[0]} 00:00:00`)),
      );
      params.append(
        "endTime",
        configure.formatDate(new Date(`${searchForm.timeRange[1]} 23:59:59`)),
      );
    }
    // 分页参数
    params.append("page", currentPage.value);
    params.append("size", pageSize.value);

    const { data: response } = await apiAjax.get(
      `/api/jnx/fusion/apps/satellite/fusion/accuracyLevel/findXtzbxwsspcPage?${params.toString()}`,
    );

    if (response) {
      tableData.value = response.content || [];
      total.value = response.totalElements || 0;
    }
  } catch (error) {
    console.error("获取数据失败:", error);
  } finally {
    loading.value = false;
  }
};

const searchForm = reactive({
  navigationSystemCode: "",

  timeRange: [],
  accuracyLevel: "",
});

// 导航系统选项
const systemOptions = computed(() => {
  if (!allSatelliteStore.data || !allSatelliteStore.data.length) return [];
  return allSatelliteStore.data.map((system) => ({
    value: system.systemCode,
    label: system.systemName,
  }));
});
// 分页相关
const currentPage = ref(1);
const pageSize = ref(10);

const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1;
  loadData();
};

const handleCurrentChange = (val) => {
  currentPage.value = val;
  loadData();
};

// 处理导航系统变更
const handleSystemChange = () => {
  // // 重新加载数据
  currentPage.value = 1;
  loadData();
};

// 搜索处理函数
const onSearch = () => {
  currentPage.value = 1;
  loadData();
};

onMounted(async () => {
  await allSatelliteStore.initAllData();
  searchForm.navigationSystemCode = allSatelliteStore.data[0].systemCode;
  loadData();
});
</script>

<style lang="scss" scoped>
.pseudo-range-info {
  height: 100%;
  .search-form {
    background-color: #fff;
    padding: 12px;
    border-radius: 4px;
    // margin-bottom: 16px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .tables-section {
    display: flex;
    gap: 20px;
    // margin-top: 20px;

    .table-wrapper {
      flex: 1;
      background-color: #fff;
      border-radius: 4px;
      // padding: 15px;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);

      .table-title {
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 15px;
        color: #303133;
        padding-left: 5px;
        border-left: 4px solid #409eff;
      }

      .pagination-wrapper {
        margin-top: 16px;
        padding: 10px;
        display: flex;
        justify-content: flex-end;
        background-color: #fff;
      }
    }
  }
}
.chart-container {
  width: 100%;

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;

    h3 {
      margin: 0;
      font-size: 16px;
      color: #303133;
    }

    .system-select {
      display: flex;
      align-items: center;

      span {
        margin-right: 8px;
        font-size: 14px;
      }
    }
  }

  .chart-content {
    width: 100%;
    height: 500px;
  }
}
</style>
