// import md5 from "js-md5";
import pageConfig from "../config";
import CryptoJS from 'crypto-js';
import { $uCommon } from "./uCommon";
import { localStg } from '@/utils/storage';
const agentInfo = process.env.NODE_ENV === "production"
//解密方法
function Decrypt(word, type) {
  let cipher = getKeyByType(type);
  let encryptedHexStr = CryptoJS.enc.Hex.parse(word);
  let srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr);
  // let srcs = CryptoJS.enc.Utf8.parse(word);
  let decrypt = CryptoJS.AES.decrypt(srcs, cipher.key, {
    iv: cipher.iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  let decryptedStr = decrypt.toString(CryptoJS.enc.Utf8);
  return decryptedStr.toString();
}
//加密方法
function Encrypt(word, type) {
  let cipher = getKeyByType(type);
  let srcs = CryptoJS.enc.Utf8.parse(word);
  let encrypted = CryptoJS.AES.encrypt(srcs, cipher.key, {
    iv: cipher.iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.ciphertext.toString().toUpperCase();
}
/**
 * 根据类型获取 密钥和偏移量
 * @param type 接口类型
 * @returns {{iv: string, key: string}}
 */
function getKeyByType(type) {
  type = type || "default";
  let key = ""; //十六位十六进制数作为密钥
  let iv = ""; //十六位十六进制数作为密钥偏移量
  if (type == "default") {
    key = CryptoJS.enc.Utf8.parse(pageConfig.publicKey);
    iv = CryptoJS.enc.Utf8.parse(pageConfig.publicKey.substr(0, 16));
  }
  return {
    key,
    iv,
  };
}
export default {
  Decrypt,
  Encrypt,
};
function HmacSHA256(word) {
  let key = CryptoJS.enc.Utf8.parse(pageConfig.publicKey); //十六位十六进制数作为密钥
  let encrypted = CryptoJS.HmacSHA256(word, key);
  var encryptedHex = CryptoJS.enc.Hex.stringify(encrypted);
  return encryptedHex;
}

/**
 * 接口加密前处理
 * @param url    接口url
 * @param params 接口参数
 * @param header 请求头
 * @param channelType 接口渠道类型，用于区分接口地址、加解密协议等
 * @returns {{baseURL, headers, params, url}}
 */
export const apiEncryptHandle = function (url, params, header, channelType) {
  let baseURL, channel, headers;
  channelType = channelType || "default";
  let channelTypeObj = {
    //默认接口
    default: () => {
      baseURL = agentInfo ? import.meta.env.VITE_SERVICE_BASE_URL : "/Api";
    },
    // 获取历史数据
    login: () => {
      baseURL = agentInfo ? import.meta.env.VITE_SERVICE_LONGS_URL : "/logApi";
    },
  };

  channelTypeObj[channelType]();
  headers = {
    "Content-Type": "application/json",
  };
  const token = localStg.get('token') || false;
  if (params) {
    try {
      params = params ? JSON.stringify(params) : "{}";
    } catch (e) {
      params = "";
    }
  }
  if (!!header) {
    for (let i in header) {
      headers[i] = header[i];
    }
  }
  if (token) {
    headers.Authorization = token;
  }
  console.log("baseURL", baseURL)
  return {
    baseURL: baseURL,
    url,
    params,
    headers,
  };
};

/**
 * 接口统一解密处理
 * @param ciphertext   密文
 * @param channelType  接口类型
 * @returns {{msg: string, data: null, status: boolean}}
 */
export const apiDecryptHandle = function (ciphertext, channelType) {
  channelType = channelType || "default";
  let decodeData = {
    status: false, //true-成功  false-失败
    data: null, //请求返回的所有参数
    msg: "返回数据格式有误！", //错误提示
  };
  if (channelType == "default") {
    ciphertext = Decrypt(ciphertext, channelType);
    let json = $uCommon.isJSON(ciphertext);
    if (!!json) {
      decodeData.data = json;
      if (json.status == 200) {
        decodeData.status = true;
        delete decodeData.msg;
      } else {
        decodeData.msg = json.msg;
      }
    }
  }

  //console.log(decodeData);
  return decodeData;
};
