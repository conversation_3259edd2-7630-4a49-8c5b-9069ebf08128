<template>
  <div class="eop-containers">
    <navList
      :navList="navItems"
      :index="activeIndex"
      @setoriData="handleNavChange"
      class="nav-list"
    />
    <GLONASS v-if="activeIndex === 1" />
    <Galileo v-if="activeIndex === 2" />
    <D1D2 v-if="activeIndex === 3" />
    <BDSCNAV v-if="activeIndex === 4" />
    <LNAV v-if="activeIndex === 5" />
    <GPSCNAV v-if="activeIndex === 6" />
  </div>
</template>

<script setup>
import { ref } from "vue";
import navList from "@/components/navList/navList.vue";
import { useRouter } from "vue-router";
import BDSCNAV from "./BDSCNAV.vue";
import GLONASS from "./GLONASS.vue";
import D1D2 from "./D1D2.vue";
import <PERSON> from "./Galileo.vue";
import LNAV from "./LNAV.vue";
import GPSCNAV from "./GPSCNAV.vue";
const router = useRouter();
const activeIndex = ref(3);

const navItems = [
  { id: 3, name: "BDS D1/D2" },
  { id: 4, name: "BDS CNAV" },
  { id: 5, name: "GPS LNAV" },
  { id: 6, name: "GPS CNAV" },
  { id: 1, name: "GLONASS" },
  { id: 2, name: "Galileo" },
];
// 将数字转换为科学计数法
const toScientificNotation = (numStr) => {
  const num = parseFloat(numStr);
  return num.toExponential(4);
};

// 处理导航切换
const handleNavChange = (id) => {
  activeIndex.value = id;
  // switch (id) {
  //   case 1:
  //     router.push("/dataManagement/rawTimeDiff/EOP/BDS");
  //     break;
  //   case 2:
  //     router.push("/dataManagement/rawTimeDiff/EOP/GLONASS");
  //     break;
  //   case 3:
  //     router.push("/dataManagement/rawTimeDiff/EOP/GPS");
  //     break;
  //   default:
  //     router.push("/dataManagement/rawTimeDiff/EOP/BDS");
  // }
};
</script>

<style scoped lang="scss">
.eop-containers {
  margin-top: 16px;
  padding: 16px;
  background: #fff;
  .nav-list {
    border-bottom: 1px solid #e5e5e5;
  }
}
</style>
