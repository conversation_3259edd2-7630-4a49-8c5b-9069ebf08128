<template>
  <div>
    <StatusPanel :timing-data="HomeData.navList.timingData" :alerts="HomeData.navList.alertsList" />
    <div class="home-container">
      <chart-section />
    </div>
  </div>
</template>

<script setup>
import StatusPanel from "@/components/status-panel/index.vue";
import ChartSection from "./components/ChartSection.vue";
import { useHome } from "@/store/modules/home";
import { ref, onMounted } from "vue";
const HomeData = useHome();
onMounted(() => {
  HomeData.init();
});
</script>

<style lang="scss" scoped>
.home-container {
  background: #f5f7fa;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-table) {
  --el-table-border-color: #ebeef5;
  --el-table-header-bg-color: #f5f7fa;
}
</style>
