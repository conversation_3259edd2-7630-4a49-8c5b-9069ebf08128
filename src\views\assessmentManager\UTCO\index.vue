<template>
  <div class="message-parameter-evaluation">
    <el-card class="box-card">
      <div class="content-wrapper">
        <!-- 搜索区域 -->
        <div class="search-section">
          <el-form :model="searchForm" inline>
            <el-form-item label="评估类型">
              <el-select
                v-model="searchForm.paramType"
                placeholder="请选择评估类型"
                style="width: 150px"
              >
                <el-option label="GNSS单星伪码授时偏差" value="A" />
                <el-option label="GNSS系统伪码授时偏差" value="B" />
                <el-option label="GNSS卫星伪码时差" value="C" />
                <el-option label="系统载波相位授时" value="D" />
                <el-option label="系统载波相位时差" value="E" />
              </el-select>
            </el-form-item>

            <el-form-item label="频点">
              <el-select
                v-model="searchForm.paramType"
                placeholder="请选择频点"
                style="width: 150px"
              >
                <el-option label="BDS-B1" value="A" />
                <el-option label="BDS-B2" value="B" />
                <el-option label="BDS-B3" value="C" />
                <el-option label="BDS-B1C" value="D" />
                <el-option label="BDS-B2a" value="E" />
                <el-option label="BDS-B3a" value="F" />
              </el-select>
            </el-form-item>

            <el-form-item label="评估周期">
              <el-select
                v-model="searchForm.cycleType"
                placeholder="请选择评估周期"
                style="width: 150px"
                @change="handleCycleTypeChange"
              >
                <el-option label="年" value="year" />
                <el-option label="月" value="month" />
                <el-option label="日" value="day" />
              </el-select>
            </el-form-item>
            <el-form-item label="时间范围">
              <template v-if="searchForm.cycleType === 'year'">
                <div class="year-range-picker">
                  <el-date-picker
                    v-model="yearStart"
                    type="year"
                    placeholder="开始年份"
                    format="YYYY"
                    value-format="YYYY"
                    @change="updateYearRange"
                    style="width: 120px"
                  />
                  <span class="range-separator">至</span>
                  <el-date-picker
                    v-model="yearEnd"
                    type="year"
                    placeholder="结束年份"
                    format="YYYY"
                    value-format="YYYY"
                    @change="updateYearRange"
                    style="width: 120px"
                  />
                </div>
              </template>
              <el-date-picker
                v-else
                v-model="searchForm.dateRange"
                :type="datePickerType"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :format="datePickerFormat"
                :value-format="dateValueFormat"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="main-content">
          <!-- 表格区域 -->
          <div class="table-section">
            <div class="section-title">数据统计表</div>
            <el-table
              :data="tableData"
              border
              stripe
              style="width: 100%"
              height="380px"
              :row-class-name="tableRowClassName"
              @row-dblclick="handleRowDblClick"
              :header-cell-style="headerCellStyle"
              :cell-style="cellStyle"
            >
              <el-table-column prop="evaluateTime" label="评估时间" />

              <el-table-column prop="maxValue" label="最大值">
                <template #default="{ row }"> {{ row.maxValue }} ns </template>
              </el-table-column>
              <el-table-column prop="minValue" label="最小值">
                <template #default="{ row }"> {{ row.minValue }} ns </template>
              </el-table-column>
              <el-table-column prop="avgValue" label="均值">
                <template #default="{ row }"> {{ row.avgValue }} ns </template>
              </el-table-column>
              <el-table-column prop="standardDev" label="标准差">
                <template #default="{ row }">
                  {{ row.standardDev }} ns
                </template>
              </el-table-column>
              <el-table-column prop="rms" label="均方根">
                <template #default="{ row }"> {{ row.rms }} ns </template>
              </el-table-column>
              <el-table-column prop="percentile" label="95%分位数">
                <template #default="{ row }">
                  {{ row.percentile }} ns
                </template>
              </el-table-column>
              <el-table-column label="操作" fixed="right" width="120">
                <template #default="{ row }">
                  <el-button type="primary" @click="handleViewDetail(row)">
                    评估详情
                  </el-button>
                </template>
              </el-table-column>
            </el-table>

            <!-- 分页 -->
            <div class="pagination-container">
              <el-pagination
                v-model:current-page="pagination.currentPage"
                v-model:page-size="pagination.pageSize"
                :page-sizes="[10, 20, 30, 50]"
                :total="pagination.total"
                layout="total, sizes, prev, pager, next, jumper"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
              />
            </div>
          </div>

          <!-- 图表区域 -->
          <div class="chart-section">
            <div class="section-title">数据可视化</div>
            <div class="chart-container">
              <line-chart ref="lineChartRef" :data="chartOptions" />
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog
      v-model="detailVisible"
      title="详细数据"
      width="70%"
      destroy-on-close
      center
    >
      <el-table
        :data="detailData"
        border
        stripe
        style="width: 100%"
        height="500px"
        :header-cell-style="headerCellStyle"
        :cell-style="cellStyle"
      >
        <el-table-column prop="time" label="时间" />

        <el-table-column prop="maxValue" label="最大值">
          <template #default="{ row }"> {{ row.maxValue }} ns </template>
        </el-table-column>
        <el-table-column prop="minValue" label="最小值">
          <template #default="{ row }"> {{ row.minValue }} ns </template>
        </el-table-column>
        <el-table-column prop="avgValue" label="均值">
          <template #default="{ row }"> {{ row.avgValue }} ns </template>
        </el-table-column>
        <el-table-column prop="standardDev" label="标准差">
          <template #default="{ row }"> {{ row.standardDev }} ns </template>
        </el-table-column>
        <el-table-column prop="rms" label="均方根">
          <template #default="{ row }"> {{ row.rms }} ns </template>
        </el-table-column>
        <el-table-column prop="percentile" label="95%分位数">
          <template #default="{ row }"> {{ row.percentile }} ns </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import LineChart from "@/components/lineChart/lineChart.vue";
// 表头样式
const headerCellStyle = {
  background: "#f5f7fa",
  color: "#606266",
  fontWeight: "bold",
  fontSize: "18px",
  height: "50px",
};

// 单元格样式
const cellStyle = {
  fontSize: "16px",
  padding: "10px 0",
};
// 搜索表单数据
const searchForm = ref({
  paramName: "",
  paramType: "",
  cycleType: "day", // 默认选择日期
  dateRange: [],
});

// 日期选择器配置
const datePickerType = ref('daterange');
const datePickerFormat = ref('YYYY-MM-DD');
const dateValueFormat = ref('YYYY-MM-DD');

// 分页配置
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});

// 表格数据
const tableData = ref([]);

// 图表实例
const lineChartRef = ref(null);

// 图表配置
const chartOptions = ref({
  title: {
    text: "",
    left: "center",
    textStyle: {
      fontSize: 16,
      fontWeight: "bold",
    },
  },
  tooltip: {
    trigger: "axis",
    axisPointer: {
      type: "shadow",
    },
    formatter: "{b}: {c} ns",
  },
  grid: {
    left: "5%",
    right: "5%",
    bottom: "8%",
    top: "15%",
    containLabel: true,
  },
  xAxis: {
    type: "category",
    data: ["最大值", "最小值", "均值", "标准差", "均方根", "95%分位数"],
    axisLabel: {
      interval: 0,
      rotate: 30,
    },
  },
  yAxis: {
    type: "value",
    name: "ns",
    nameTextStyle: {
      padding: [0, 0, 0, 40],
    },
  },
  series: [
    {
      type: "bar",
      barWidth: "40%",
      itemStyle: {
        color: "#409EFF",
        borderRadius: [4, 4, 0, 0],
      },
      data: [],
    },
  ],
});

// 详情弹窗控制
const detailVisible = ref(false);
const detailData = ref([]);

// 选中行的索引
const selectedRowIndex = ref(-1);

// 查询方法
const handleSearch = () => {
  loadTableData();
};

// 重置方法
const handleReset = () => {
  searchForm.value = {
    paramName: "",
    paramType: "",
    cycleType: "day", // 重置为默认值
    dateRange: [],
  };
  // 重置日期选择器类型
  datePickerType.value = 'daterange';
  datePickerFormat.value = 'YYYY-MM-DD';
  dateValueFormat.value = 'YYYY-MM-DD';
  loadTableData();
};

// 分页大小改变
const handleSizeChange = (val) => {
  pagination.value.pageSize = val;
  loadTableData();
};

// 当前页改变
const handleCurrentChange = (val) => {
  pagination.value.currentPage = val;
  loadTableData();
};

// 查看详情
const handleViewDetail = (row) => {
  detailVisible.value = true;
  // 模拟获取详细数据
  detailData.value = Array.from({ length: 24 }, (_, index) => ({
    time: `${row.evaluateTime} ${index.toString().padStart(2, "0")}:00:00`,
    error: (Math.random() * 10 - 5).toFixed(3),
    maxValue: (Math.random() * 15).toFixed(3),
    minValue: (Math.random() * -5).toFixed(3),
    avgValue: (Math.random() * 5).toFixed(3),
    standardDev: (Math.random() * 2).toFixed(3),
    rms: (Math.random() * 8).toFixed(3),
    percentile: (Math.random() * 12).toFixed(3),
  }));
};

// 更新图表数据的方法
const updateChartData = (row) => {
  if (!row) return;

  // 更新图表标题
  chartOptions.value.title.text = `评估时间: ${row.evaluateTime}`;

  // 更新图表数据
  chartOptions.value.series[0].data = [
    parseFloat(row.maxValue),
    parseFloat(row.minValue),
    parseFloat(row.avgValue),
    parseFloat(row.standardDev),
    parseFloat(row.rms),
    parseFloat(row.percentile),
  ];
};

// 修改双击行事件处理
const handleRowDblClick = (row, column, event) => {
  selectedRowIndex.value = tableData.value.findIndex((item) => item === row);
  updateChartData(row);
};

// 修改加载表格数据方法
const loadTableData = () => {
  // 模拟异步请求
  const mockData = Array.from({ length: 50 }, (_, index) => {
    const date = new Date();
    date.setDate(date.getDate() - index);
    return {
      evaluateTime: date.toISOString().split("T")[0],
      error: (Math.random() * 10 - 5).toFixed(3),
      maxValue: (Math.random() * 15).toFixed(3),
      minValue: (Math.random() * -5).toFixed(3),
      avgValue: (Math.random() * 5).toFixed(3),
      standardDev: (Math.random() * 2).toFixed(3),
      rms: (Math.random() * 8).toFixed(3),
      percentile: (Math.random() * 12).toFixed(3),
    };
  });

  // 计算分页
  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;

  tableData.value = mockData.slice(start, end);
  pagination.value.total = mockData.length;

  // 默认选中第一行并更新图表
  if (tableData.value.length > 0) {
    selectedRowIndex.value = 0;
    updateChartData(tableData.value[0]);
  }
};

// 设置行的类名
const tableRowClassName = ({ row, rowIndex }) => {
  if (rowIndex === selectedRowIndex.value) {
    return "selected-row";
  }
  return "";
};

// 年份选择
const yearStart = ref('');
const yearEnd = ref('');

// 更新年份范围
const updateYearRange = () => {
  if (yearStart.value && yearEnd.value) {
    searchForm.value.dateRange = [yearStart.value, yearEnd.value];
  }
};

// 处理周期类型变化
const handleCycleTypeChange = (value) => {
  switch(value) {
    case 'year':
      datePickerFormat.value = 'YYYY';
      dateValueFormat.value = 'YYYY';
      // 重置年份选择器的值
      yearStart.value = '';
      yearEnd.value = '';
      break;
    case 'month':
      datePickerType.value = 'monthrange';
      datePickerFormat.value = 'YYYY-MM';
      dateValueFormat.value = 'YYYY-MM';
      break;
    case 'day':
    default:
      datePickerType.value = 'daterange';
      datePickerFormat.value = 'YYYY-MM-DD';
      dateValueFormat.value = 'YYYY-MM-DD';
      break;
  }
  // 清空已选日期范围
  searchForm.value.dateRange = [];
};

// 选择器配置项
const pickerOptions = computed(() => {
  if (searchForm.value.cycleType === 'year') {
    return {
      type: 'year'
    };
  }
  return {};
});

onMounted(() => {
  loadTableData();
});
</script>

<style lang="scss" scoped>
.message-parameter-evaluation {
  margin-top: 12px;

  .box-card {
    border-radius: 8px;
    background-color: rgba(240, 242, 245, 0);
    .card-header {
      padding: 16px 20px;
      background-color: #f0f8ff;
      border-bottom: 1px solid #e0e7ff;

      h2 {
        margin: 0;
        color: #2c3e50;
        font-size: 20px;
      }
    }

    .content-wrapper {
      // background-color: #edeff2;
      .search-section {
        padding: 16px;
        background-color: #f9fafc;
        border-radius: 6px;
        margin-bottom: 20px;
      }

      .main-content {
        display: flex;
        flex-direction: column;
      }

      :deep(.el-form-item) {
        margin-bottom: 0;
      }

      .section-title {
        font-size: 16px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 12px;
        padding-left: 8px;
        border-left: 4px solid #409eff;
      }

      .table-section {
        margin-bottom: 20px;
        background-color: #fff;
        border-radius: 6px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        padding: 16px;

        .pagination-container {
          margin-top: 20px;
          display: flex;
          justify-content: flex-end;
        }
      }

      .chart-section {
        background-color: #fff;
        border-radius: 6px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
        padding: 16px;

        .chart-container {
          height: 330px;
        }
      }
    }
  }
}

:deep(.el-card__body) {
  padding: 0;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f0f7ff !important;
  color: #304156;
}

:deep(.selected-row) {
  background-color: #ecf5ff !important;
  td {
    background-color: #ecf5ff !important;
  }
}

// 如果需要鼠标悬停效果也保持蓝色
:deep(.selected-row:hover) {
  td {
    background-color: #e0f0ff !important;
  }
}

:deep(.el-button--primary) {
  background-color: #1890ff;
  border-color: #1890ff;

  &:hover,
  &:focus {
    background-color: #40a9ff;
    border-color: #40a9ff;
  }
}

:deep(.el-pagination) {
  padding: 0;
  font-weight: normal;
}

.year-range-picker {
  display: flex;
  align-items: center;

  .range-separator {
    margin: 0 8px;
  }
}
</style>
